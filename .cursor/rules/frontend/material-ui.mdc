---
description: 
globs: 
alwaysApply: false
---
# Material-UI Best Practices

## Objective
Ensure consistent, performant, and maintainable usage of Material-UI components with proper theming and styling.

## Context
- Material-UI v6 (@mui/material, @mui/joy, @mui/lab)
- Custom theme configuration with styled components
- Emotion as the CSS-in-JS solution
- mergeClasses utility for class composition

## Rules

### Component Import Patterns
- **Named Imports**: Import specific components, not entire modules
  ```typescript
  // ✅ Good
  import Box from '@mui/material/Box';
  import Button from '@mui/material/Button';
  import { styled } from '@mui/material/styles';
  
  // ❌ Avoid
  import * as MUI from '@mui/material';
  import { Box, Button } from '@mui/material'; // Bundle size impact
  ```

- **Type Imports**: Import prop types when needed
  ```typescript
  // ✅ Good
  import type { ButtonProps } from '@mui/material/Button';
  import type { SxProps, Theme } from '@mui/material/styles';
  
  interface CustomButtonProps extends ButtonProps {
    customProp?: string;
  }
  ```

### Styling Patterns
- **Styled Components**: Use for reusable styled components
  ```typescript
  // ✅ Good
  const StyledButton = styled(Button)(({ theme }) => ({
    borderRadius: theme.spacing(1),
    textTransform: 'none',
    '&:hover': {
      backgroundColor: theme.palette.primary.light,
    },
  }));
  
  // ❌ Avoid inline styles for complex styling
  <Button style={{ borderRadius: 8, textTransform: 'none' }} />
  ```

- **sx Prop**: Use for simple, component-specific styles
  ```typescript
  // ✅ Good - Simple styling
  <Box
    sx={{
      p: 2,
      borderRadius: 1,
      backgroundColor: 'background.paper',
    }}
  >
    Content
  </Box>
  
  // ❌ Avoid sx for complex styles that could be reused
  <Box
    sx={{
      p: 2,
      borderRadius: 1,
      backgroundColor: 'background.paper',
      '&:hover': { /* complex hover styles */ },
      '& .child-element': { /* nested styles */ },
    }}
  >
  ```

- **Class-based Styling**: Use for consistent component styling
  ```typescript
  // ✅ Good - classes.ts file
  export const logoClasses = {
    root: 'logo__root',
    disabled: 'logo__disabled',
  };
  
  // Component usage
  <LogoRoot
    className={mergeClasses([
      logoClasses.root,
      disabled && logoClasses.disabled,
      className
    ])}
  />
  ```

### Theme Integration
- **Theme Access**: Use theme values consistently
  ```typescript
  // ✅ Good
  const StyledCard = styled(Card)(({ theme }) => ({
    padding: theme.spacing(2),
    borderRadius: theme.shape.borderRadius,
    boxShadow: theme.shadows[1],
    color: theme.palette.text.primary,
  }));
  
  // ❌ Avoid hardcoded values
  const StyledCard = styled(Card)({
    padding: '16px',
    borderRadius: '4px',
    boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
  });
  ```

- **Responsive Design**: Use theme breakpoints
  ```typescript
  // ✅ Good
  const ResponsiveBox = styled(Box)(({ theme }) => ({
    padding: theme.spacing(1),
    [theme.breakpoints.up('md')]: {
      padding: theme.spacing(2),
    },
    [theme.breakpoints.up('lg')]: {
      padding: theme.spacing(3),
    },
  }));
  ```

### Component Composition
- **Prop Forwarding**: Properly forward props for extensible components
  ```typescript
  // ✅ Good
  interface CustomCardProps extends Omit<CardProps, 'variant'> {
    variant?: 'elevated' | 'outlined' | 'filled';
    customProp?: string;
  }
  
  export const CustomCard = forwardRef<HTMLDivElement, CustomCardProps>(
    ({ variant = 'elevated', customProp, children, ...other }, ref) => {
      return (
        <Card
          ref={ref}
          variant={variant === 'filled' ? 'elevation' : variant}
          {...other}
        >
          {children}
        </Card>
      );
    }
  );
  ```

- **Slot Customization**: Use slots for flexible component APIs
  ```typescript
  // ✅ Good
  interface DataTableProps {
    data: any[];
    loading?: boolean;
    slots?: {
      toolbar?: React.ComponentType;
      noData?: React.ComponentType;
      loadingOverlay?: React.ComponentType;
    };
    slotProps?: {
      toolbar?: object;
      noData?: object;
    };
  }
  ```

### Icon Usage
- **Icon Imports**: Use specific icon imports
  ```typescript
  // ✅ Good
  import EditIcon from '@mui/icons-material/Edit';
  import DeleteIcon from '@mui/icons-material/Delete';
  
  // ❌ Avoid
  import * as Icons from '@mui/icons-material';
  const EditIcon = Icons.Edit;
  ```

- **Iconify Integration**: Use Iconify for custom icons
  ```typescript
  // ✅ Good
  import { Iconify } from 'src/components/iconify';
  
  <Iconify icon="solar:edit-2-linear" width={20} />
  ```

### Form Components
- **Form Integration**: Integrate with react-hook-form properly
  ```typescript
  // ✅ Good
  import { Controller } from 'react-hook-form';
  
  <Controller
    name="email"
    control={control}
    render={({ field, fieldState: { error } }) => (
      <TextField
        {...field}
        label="Email"
        error={!!error}
        helperText={error?.message}
        fullWidth
      />
    )}
  />
  ```

- **Validation States**: Handle validation consistently
  ```typescript
  // ✅ Good
  <TextField
    value={value}
    onChange={onChange}
    error={!!error}
    helperText={error || 'Enter your email address'}
    InputProps={{
      endAdornment: isValidating ? (
        <CircularProgress size={20} />
      ) : undefined,
    }}
  />
  ```

### Layout Components
- **Grid System**: Use Grid2 for layouts
  ```typescript
  // ✅ Good
  import Grid from '@mui/material/Grid2';
  
  <Grid container spacing={2}>
    <Grid xs={12} md={6}>
      <Card>Content 1</Card>
    </Grid>
    <Grid xs={12} md={6}>
      <Card>Content 2</Card>
    </Grid>
  </Grid>
  ```

- **Stack Component**: Use for simple flexbox layouts
  ```typescript
  // ✅ Good
  <Stack direction="row" spacing={2} alignItems="center">
    <Avatar src={user.avatar} />
    <Box>
      <Typography variant="h6">{user.name}</Typography>
      <Typography variant="body2" color="text.secondary">
        {user.email}
      </Typography>
    </Box>
  </Stack>
  ```

### Performance Optimization
- **Dynamic Imports**: Use for large component sets
  ```typescript
  // ✅ Good for data grid
  const DataGrid = lazy(() => import('@mui/x-data-grid').then(module => ({
    default: module.DataGrid
  })));
  ```

- **Prop Memoization**: Memoize complex props
  ```typescript
  // ✅ Good
  const tableColumns = useMemo(() => [
    { field: 'id', headerName: 'ID', width: 90 },
    { field: 'name', headerName: 'Name', width: 150 },
  ], []);
  
  <DataGrid columns={tableColumns} rows={rows} />
  ```

### Accessibility
- **ARIA Properties**: Include proper ARIA attributes
  ```typescript
  // ✅ Good
  <IconButton
    onClick={onEdit}
    aria-label="Edit user profile"
    size="small"
  >
    <EditIcon />
  </IconButton>
  
  <TextField
    label="Password"
    type={showPassword ? 'text' : 'password'}
    aria-describedby="password-helper-text"
    InputProps={{
      endAdornment: (
        <IconButton
          onClick={() => setShowPassword(!showPassword)}
          aria-label="Toggle password visibility"
        >
          {showPassword ? <VisibilityOff /> : <Visibility />}
        </IconButton>
      ),
    }}
  />
  ```

### Common Patterns
- **Loading States**: Consistent loading indicators
  ```typescript
  // ✅ Good
  {loading ? (
    <Box display="flex" justifyContent="center" p={4}>
      <CircularProgress />
    </Box>
  ) : (
    <DataContent data={data} />
  )}
  ```

- **Error States**: Consistent error handling
  ```typescript
  // ✅ Good
  {error ? (
    <Alert severity="error" sx={{ mb: 2 }}>
      {error.message || 'An error occurred'}
    </Alert>
  ) : null}
  ```

- **Empty States**: Consistent empty state components
  ```typescript
  // ✅ Good
  {data.length === 0 ? (
    <Box textAlign="center" py={4}>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        No data available
      </Typography>
      <Button variant="contained" startIcon={<AddIcon />}>
        Add New Item
      </Button>
    </Box>
  ) : (
    <DataList data={data} />
  )}
  ```

## Exceptions
- **Custom themes**: Alternative styling approaches for branded components
- **Performance requirements**: Direct DOM manipulation for highly interactive components
- **Third-party integration**: Follow component library patterns for wrapped components
