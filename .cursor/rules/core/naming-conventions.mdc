---
description: 
globs: 
alwaysApply: true
---
# Naming Conventions

## Objective
Establish consistent naming patterns across the entire codebase to improve readability, maintainability, and team collaboration.

## Context
- React + TypeScript application using Material-UI
- Feature-based folder structure with barrel exports
- Strict TypeScript configuration
- ESLint with custom import ordering rules

## Rules

### File and Folder Naming
- **Folders**: Use kebab-case for all directory names
  - ✅ `auth-context`, `user-management`, `file-upload`
  - ❌ `AuthContext`, `userManagement`, `file_upload`

- **React Components**: Use kebab-case for file names, PascalCase for component names
  - ✅ `user-profile.tsx` exports `UserProfile`
  - ✅ `custom-dialog.tsx` exports `CustomDialog`
  - ❌ `UserProfile.tsx`, `customDialog.tsx`

- **Hooks**: Use kebab-case with `use-` prefix
  - ✅ `use-auth-context.ts`, `use-local-storage.ts`
  - ❌ `useAuthContext.ts`, `auth-hook.ts`

- **Utilities**: Use kebab-case describing the utility purpose
  - ✅ `format-time.ts`, `email-validation.ts`
  - ❌ `utils.ts`, `helpers.ts`, `formatTime.ts`

- **Types**: Use kebab-case for type definition files
  - ✅ `user-types.ts`, `api-response.ts`
  - ❌ `UserTypes.ts`, `types.ts`

### Component Naming
- **React Components**: Use PascalCase with descriptive names
  - ✅ `UserProfile`, `CustomDialog`, `FileUploadButton`
  - ❌ `userProfile`, `dialog`, `Button`

- **Component Props**: Use PascalCase with `Props` suffix
  - ✅ `interface UserProfileProps`, `type CustomDialogProps`
  - ❌ `interface UserProfileProperties`, `type DialogProps`

### Variable and Function Naming
- **Variables**: Use camelCase with descriptive names
  - ✅ `isAuthenticated`, `userProfile`, `fileUploadProgress`
  - ❌ `auth`, `user`, `progress`

- **Functions**: Use camelCase with verb-noun pattern
  - ✅ `handleSubmit`, `fetchUserData`, `validateEmail`
  - ❌ `submit`, `userData`, `email`

- **Constants**: Use SCREAMING_SNAKE_CASE for global constants
  - ✅ `API_BASE_URL`, `MAX_FILE_SIZE`, `DEFAULT_TIMEOUT`
  - ❌ `apiBaseUrl`, `maxFileSize`

### Type Naming
- **Interfaces**: Use PascalCase with descriptive names, avoid `I` prefix
  - ✅ `interface User`, `interface ApiResponse<T>`
  - ❌ `interface IUser`, `interface UserInterface`

- **Type Aliases**: Use PascalCase with descriptive names
  - ✅ `type UserRole`, `type ApiError`
  - ❌ `type userRole`, `type Error`

- **Generic Types**: Use single uppercase letters starting with `T`
  - ✅ `<T>`, `<T, U>`, `<TData, TError>`
  - ❌ `<Type>`, `<Data>`

### Event Handler Naming
- **Event Handlers**: Use `handle` prefix followed by event description
  - ✅ `handleClick`, `handleSubmit`, `handleFileChange`
  - ❌ `onClick`, `submit`, `fileChange`

- **Callback Props**: Use `on` prefix for callback properties
  - ✅ `onSubmit`, `onFileSelect`, `onUserChange`
  - ❌ `handleSubmit`, `fileSelect`, `userChange`

## Exceptions
- **Third-party library requirements**: Follow library conventions when required (e.g., Next.js pages)
- **Legacy code**: Maintain consistency within existing modules during refactoring
- **API responses**: Mirror backend naming when necessary for data mapping
