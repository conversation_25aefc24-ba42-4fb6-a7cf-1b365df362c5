---
description: 
globs: 
alwaysApply: true
---
# Project Structure & File Organization

## Objective
Maintain a consistent, scalable file organization that supports feature-based development and clear separation of concerns.

## Context
- React + TypeScript application with Vite build tool
- Feature-based architecture with domain-driven organization
- Material-UI components with custom styling
- Redux Toolkit for state management
- Firebase authentication and hosting

## Rules

### Top-Level Directory Structure
```
src/
├── app.tsx              # Main application entry
├── global.css           # Global styles
├── global-config.ts     # Application-wide configuration
├── assets/              # Static assets (icons, images, data)
├── auth/                # Authentication domain
├── components/          # Reusable UI components
├── contexts/            # React contexts (shared state)
├── hooks/               # Custom React hooks
├── layouts/             # Application layouts
├── lib/                 # External library configurations
├── locales/             # Internationalization
├── pages/               # Route components
├── routes/              # Routing configuration
├── sections/            # Page-specific components
├── store/               # Redux store configuration
├── theme/               # UI theme configuration
├── types/               # TypeScript type definitions
└── utils/               # Utility functions
```

### Component Organization
- **Atomic Components**: Place in `src/components/[component-name]/`
  - ✅ `src/components/custom-dialog/`
  - ✅ `src/components/file-thumbnail/`
  - ❌ `src/components/dialogs/custom-dialog/`

- **Feature Components**: Place in `src/sections/[feature]/`
  - ✅ `src/sections/auth/login-form.tsx`
  - ✅ `src/sections/projects/project-list.tsx`
  - ❌ `src/components/auth/login-form.tsx`

- **Layout Components**: Place in `src/layouts/[layout-type]/`
  - ✅ `src/layouts/dashboard/`
  - ✅ `src/layouts/auth-split/`
  - ❌ `src/components/layouts/`

### Component Directory Structure
Each component directory should contain:
```
component-name/
├── index.ts             # Barrel export
├── component-name.tsx   # Main component
├── classes.ts           # CSS class definitions (if needed)
├── types.ts             # Component-specific types (if complex)
└── components/          # Sub-components (if any)
    ├── sub-component.tsx
    └── index.ts
```

### Feature Domain Organization
For complex features, use domain-driven structure:
```
auth/
├── components/          # Auth-specific components
├── context/             # Auth context and providers
├── guard/               # Route guards and protection
├── hooks/               # Auth-related hooks
├── types.ts             # Auth type definitions
├── utils/               # Auth utility functions
└── view/                # Auth page components
```

### Utility Organization
- **Generic Utils**: `src/utils/[purpose].ts`
  - ✅ `format-time.ts`, `email-validation.ts`
  - ❌ `utils.ts`, `helpers.ts`

- **Domain-Specific Utils**: Within feature directories
  - ✅ `src/auth/utils/error-message.ts`
  - ✅ `src/store/utils/persist-config.ts`

### Type Definitions
- **Global Types**: `src/types/[domain].ts`
  - ✅ `src/types/common.ts`, `src/types/api.ts`
  - ❌ `src/types/index.ts` (avoid single file for all types)

- **Feature Types**: Within feature directories
  - ✅ `src/auth/types.ts`
  - ✅ `src/components/table/types.ts`

### Asset Organization
```
src/assets/
├── data/                # Static data files
│   ├── countries.ts
│   └── index.ts
├── icons/               # SVG icon components
│   ├── ai-agent-icon.tsx
│   └── index.ts
└── illustrations/       # Illustration components
    ├── background-shape.tsx
    └── index.ts
```

### Barrel Export Rules
- **Always provide index.ts** for directories with multiple exports
- **Named exports only** - avoid default exports in barrel files
- **Re-export pattern**:
  ```typescript
  // ✅ Good
  export { UserProfile } from './user-profile';
  export { UserSettings } from './user-settings';
  export type { UserProfileProps } from './user-profile';
  
  // ❌ Avoid
  export * from './user-profile';
  ```

### Import Path Guidelines
- **Absolute imports** for src/ directory using alias
  - ✅ `import { UserProfile } from 'src/components/user-profile';`
  - ❌ `import { UserProfile } from '../../../components/user-profile';`

- **Relative imports** for same-level or parent directories
  - ✅ `import { validateEmail } from './utils';`
  - ✅ `import { UserType } from '../types';`

### File Naming in Directories
- **Components**: `component-name.tsx`
- **Hooks**: `use-hook-name.ts`
- **Utils**: `function-purpose.ts`
- **Types**: `types.ts` or `domain-types.ts`
- **Constants**: `constants.ts` or `config.ts`
- **Styles**: `classes.ts` or `styles.ts`

## Exceptions
- **Third-party integrations**: Follow library-specific structure requirements
- **Legacy modules**: Maintain consistency within existing large modules
- **Build artifacts**: Configuration files can remain in root directory
- **Public assets**: Static files remain in public/ directory
