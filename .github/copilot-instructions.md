# Aida - AI-Powered Meeting Assistant

## Project Overview
Aida is a React TypeScript web application that provides AI-powered meeting assistance with live transcription, note-taking, resource management, and collaborative project features. The application serves as a comprehensive platform for meeting management and knowledge collaboration.

## Core Technologies & Dependencies

### Framework & Build Tools
- **React 18.3.1** with TypeScript for the frontend
- **Vite 6.0.3** for build tooling and development server
- **Node.js 22.x** as the runtime environment
- **Firebase 11.8.1** for backend services (authentication, storage, database)

### UI Framework & Styling
- **Material-UI (MUI) v6** ecosystem:
  - `@mui/material` for core components
  - `@mui/joy` for additional UI components
  - `@mui/x-data-grid`, `@mui/x-date-pickers`, `@mui/x-tree-view` for advanced components
- **Emotion** for CSS-in-JS styling
- **Framer Motion** for animations and transitions
- **Fontsource** for typography (Inter, DM Sans, Nunito Sans, Public Sans, Barlow)

### State Management & Data Fetching
- **Redux Toolkit (RTK) 2.5.0** for global state management
- **RTK Query** with GraphQL support for API data fetching
- **React Redux 9.2.0** for React-Redux integration
- **Redux Persist** for state persistence
- **SWR** for additional data fetching needs

### Real-time Features & Media Processing
- **Speechmatics SDK** for live transcription:
  - `@speechmatics/real-time-client-react`
  - `@speechmatics/browser-audio-input-react`
- **WaveSurfer.js** for audio visualization and playback
- **React Player** for video playback

### Content & Document Processing
- **TipTap** editor suite for rich text editing:
  - Core editor with extensions for code blocks, images, links, placeholders
- **React Markdown** with syntax highlighting (rehype-highlight, remark-gfm)
- **Marked** for additional markdown processing
- **Turndown** for HTML to markdown conversion
- **DOCX** for Word document generation
- **React PDF** for PDF document handling

### Routing & Navigation
- **React Router v7** for client-side routing
- Custom routing configuration with protected routes and lazy loading

### Development & Quality Tools
- **ESLint 9.16.0** with TypeScript support and multiple plugins
- **Prettier 3.4.2** for code formatting
- **TypeScript 5.7.2** for type safety
- **GraphQL Code Generator** for type-safe API client generation

## Project Structure

### Core Application (`src/`)
```
src/
├── app.tsx                 # Main app component with providers
├── global-config.ts        # Environment and configuration settings
├── global.css             # Global styles
└── main.tsx               # Application entry point
```

### Authentication System (`src/auth/`)
- **Firebase Authentication** integration
- Email/password authentication
- Microsoft OAuth integration
- Authentication guards and context providers
- Protected route components

### Components (`src/components/`)
- **Reusable UI Components**: Custom components built on MUI
- **Navigation**: Nav sections, breadcrumbs, menu components  
- **Form Controls**: Custom form inputs and validation
- **Data Display**: Tables, cards, lists, file thumbnails
- **Feedback**: Modals, dialogs, notifications, loading states
- **Layout**: Grid systems, containers, spacing utilities

### Hooks (`src/hooks/`)
- **Live Transcription** (`use-live-transcription.ts`): Real-time audio processing
- **AI Agent** (`ai-agent.ts`): AI integration for chat and suggestions
- **User Management**: Authentication state, user context, permissions
- **Data Fetching**: Custom hooks for API interactions

### Layouts (`src/layouts/`)
- **Dashboard Layout**: Main app layout with navigation and header
- **Auth Split Layout**: Authentication pages with split-screen design  
- **Simple Layout**: Basic layout for error and utility pages
- **Responsive Design**: Breakpoint-based layout adaptations

### Routing (`src/routes/`)
- **Route Configuration**: Organized by feature area
- **Protected Routes**: Authentication guards
- **Lazy Loading**: Code splitting for performance
- **Path Constants**: Centralized route definitions

### Store & API (`src/store/`)
- **Redux Store Configuration**: RTK setup with persistence
- **API Services**: RTK Query services for:
  - Projects and project management
  - Resources (files, documents, media)
  - Users and authentication
  - Live transcription data

### Feature Sections (`src/sections/`)

#### Projects (`src/sections/projects/`)
- **Project Management**: CRUD operations for projects
- **Folder Organization**: Hierarchical project structure
- **Team Collaboration**: Project sharing and permissions
- **Live Transcription Integration**: Real-time meeting transcription
- **AI Chat**: Project-specific AI assistance
- **Files Panel**: Resource management within projects

#### Resources (`src/sections/resources/`)
- **File Management**: Upload, organize, and preview files
- **Media Support**: Audio, video, document, and image handling
- **Transcription Services**: Automated transcription for media files
- **AI Integration**: Chat functionality for resource analysis
- **Search & Filter**: Advanced resource discovery
- **Background Processing**: Async file upload and processing

#### Recordings (`src/sections/recordings/`)
- **Meeting Recording**: Audio/video capture functionality
- **Live Transcription**: Real-time speech-to-text during recordings
- **Playback Interface**: Audio visualization with WaveSurfer.js
- **Export Features**: Multiple format support for recordings

#### Notes (`src/sections/notes/`)
- **Rich Text Editing**: TipTap-based note editor
- **Real-time Collaboration**: Shared note editing
- **AI Assistance**: AI-powered note suggestions and summaries
- **Organization**: Categorization and tagging system

#### Chat (`src/sections/chat/`)
- **AI Chat Interface**: Conversational AI assistant
- **Context Awareness**: Project and resource-specific chat
- **Message History**: Persistent chat conversations
- **Multiple AI Agents**: Different AI personalities/capabilities

### Theme & Styling (`src/theme/`)
- **Material-UI Customization**: Custom theme configuration
- **Dark/Light Mode**: Theme switching capability
- **CSS Variables**: Dynamic theming system
- **Component Overrides**: Consistent design system

### Utilities (`src/utils/`)
- **Data Formatting**: Number, date, and text utilities
- **File Handling**: File type detection and processing
- **Validation**: Form and data validation helpers
- **API Helpers**: Request/response processing utilities

## Key Features

### 🎙️ Live Transcription
- Real-time speech-to-text using Speechmatics WebSocket API
- Multi-language support with automatic language detection
- Live transcript editing and correction capabilities
- Integration with meeting recordings and project notes

### 📁 Project & Resource Management
- Hierarchical project organization with folders
- Comprehensive file management (upload, preview, organize)
- Support for multiple file types: audio, video, documents, images
- Automated transcription for audio/video content
- Resource sharing and collaborative access controls

### 🤖 AI Integration
- Context-aware AI chat assistant
- AI-powered note suggestions and content generation
- Resource analysis and summarization
- Project-specific AI agents with different capabilities

### 👥 Collaboration Features
- Team project sharing with role-based permissions
- Real-time collaborative note editing
- Project member management and invitations
- Activity tracking and notifications

### 🎨 User Experience
- Modern Material Design UI with customizable themes
- Responsive design for desktop and mobile devices
- Intuitive drag-and-drop file uploads
- Real-time feedback and progress indicators
- Comprehensive search and filtering capabilities

## Development Configuration

### Build Scripts
- `dev`: Development server with CORS enabled
- `build:production/development/staging`: Environment-specific builds
- `deploy`: Firebase deployment
- `lint`/`lint:fix`: ESLint code quality checks
- `fm:check`/`fm:fix`: Prettier code formatting

### Environment Setup
- Node.js 22.x required
- Firebase project configuration needed
- Speechmatics API credentials required
- Development uses Vite with hot module replacement

### Code Quality
- TypeScript strict mode enabled
- ESLint with comprehensive rule set including:
  - Import/export validation
  - React hooks rules
  - Code organization (Perfectionist plugin)
  - Unused import detection
- Prettier for consistent code formatting
- Pre-commit hooks for code quality enforcement

## Architecture Patterns

### Component Architecture
- **Compound Components**: Complex UI components broken into sub-components
- **Render Props**: Flexible component composition patterns
- **Custom Hooks**: Business logic extraction and reusability
- **Context Providers**: State management for feature areas

### Data Flow
- **Unidirectional Data Flow**: Redux for global state management
- **Server State Caching**: RTK Query for API data management
- **Optimistic Updates**: Immediate UI feedback with rollback capability
- **Real-time Synchronization**: WebSocket integration for live features

### Performance Optimizations
- **Code Splitting**: Route-based lazy loading
- **Bundle Analysis**: Webpack bundle optimization
- **Image Optimization**: Responsive images and lazy loading  
- **Memoization**: React.memo and useMemo for expensive operations

## Security & Best Practices

### Authentication & Authorization
- Firebase Authentication with secure token management
- Role-based access control for projects and resources
- Protected routes with authentication guards
- Secure API communication with authenticated requests

### Data Protection
- Client-side input validation and sanitization
- Secure file upload handling with type validation
- Environment variable management for sensitive configuration
- CORS configuration for API security

This documentation provides comprehensive context for understanding the Aida codebase structure, key technologies, and architectural decisions. Use this as a reference when working on any part of the application.