name: Semantic Release

on:
  workflow_run:
    workflows: ["Deploy to Production"]
    types:
      - completed
    branches:
      - develop
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run in dry-run mode (no actual release)'
        required: false
        type: boolean
        default: false

env:
  NODE_VERSION: '22'
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

jobs:
  semantic-release:
    name: Create Semantic Release
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    permissions:
      contents: write
      pull-requests: read
      issues: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'

      - name: Enable Corepack
        run: corepack enable

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Get last release tag
        id: last_release
        run: |
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$LAST_TAG" ]; then
            LAST_TAG=$(git rev-list --max-parents=0 HEAD)
          fi
          echo "tag=$LAST_TAG" >> $GITHUB_OUTPUT
          echo "Last release tag: $LAST_TAG"

      - name: Get merged PRs since last release
        id: get_prs
        run: |
          # Get commit range
          if [ "${{ steps.last_release.outputs.tag }}" = "" ]; then
            echo "No previous release found, analyzing all commits"
            COMMITS=$(git rev-list HEAD)
          else
            echo "Analyzing commits since tag: ${{ steps.last_release.outputs.tag }}"
            COMMITS=$(git rev-list ${{ steps.last_release.outputs.tag }}..HEAD)
          fi
          
          echo "Found $(echo "$COMMITS" | wc -l) commits to analyze"
          
          # Extract PR numbers from commit messages
          if [ ! -z "$COMMITS" ]; then
            PR_NUMBERS=$(echo "$COMMITS" | xargs git show --format="%s" -s | grep -oE '#[0-9]+' | sed 's/#//' | sort -u || echo "")
          else
            PR_NUMBERS=""
          fi
          
          if [ -z "$PR_NUMBERS" ]; then
            echo "No PR numbers found in commit messages"
          else
            echo "Found PR numbers: $(echo "$PR_NUMBERS" | tr '\n' ' ')"
          fi
          
          # Get PR details with labels
          PR_DATA="[]"
          if [ ! -z "$PR_NUMBERS" ]; then
            for pr_num in $PR_NUMBERS; do
              echo "Processing PR #$pr_num..."
              PR_INFO=$(gh pr view $pr_num --json number,title,labels,author,mergeCommit --jq '{number: .number, title: .title, labels: [.labels[].name], author: .author.login, mergeCommit: .mergeCommit.oid}' 2>/dev/null || echo "null")
              if [ "$PR_INFO" != "null" ] && [ "$PR_INFO" != "{}" ]; then
                PR_DATA=$(echo "$PR_DATA" | jq --argjson pr "$PR_INFO" '. + [$pr]')
              fi
            done
          fi
          
          # Ensure PR_DATA is valid JSON
          if ! echo "$PR_DATA" | jq empty 2>/dev/null; then
            echo "Invalid JSON generated, falling back to empty array"
            PR_DATA="[]"
          fi
          
          # Use heredoc format for multi-line JSON output
          echo "prs<<EOF" >> $GITHUB_OUTPUT
          echo "$PR_DATA" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          echo "Found PRs: $PR_DATA"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Calculate semantic version
        id: calc_version
        run: |
          # Get current version from package.json
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "Current version: $CURRENT_VERSION"
          
          # Parse current version
          IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
          MAJOR=${VERSION_PARTS[0]}
          MINOR=${VERSION_PARTS[1]}
          PATCH=${VERSION_PARTS[2]}
          
          # Analyze PR labels to determine version bump
          PRS='${{ steps.get_prs.outputs.prs }}'
          
          # Check if ALL PRs have skip labels
          TOTAL_PRS=$(echo "$PRS" | jq length)
          if [ "$TOTAL_PRS" -gt 0 ]; then
            SKIP_PRS=$(echo "$PRS" | jq '[.[] | select(.labels[]? | test("^(release: skip|skip-changelog|no-release)$"; "i"))] | length')
            echo "Total PRs: $TOTAL_PRS, Skip PRs: $SKIP_PRS"
            
            if [ "$SKIP_PRS" -eq "$TOTAL_PRS" ]; then
              echo "🔄 All PRs have skip labels - no release needed"
              echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
              echo "bump_type=none" >> $GITHUB_OUTPUT
              echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
              echo "skip_reason=all_prs_skipped" >> $GITHUB_OUTPUT
              exit 0
            fi
          fi
          
          HAS_MAJOR=$(echo "$PRS" | jq -r '.[] | select(.labels[]? | test("^release: major$"; "i")) | .number' | head -1)
          HAS_MINOR=$(echo "$PRS" | jq -r '.[] | select(.labels[]? | test("^release: minor$"; "i")) | .number' | head -1)
          HAS_PATCH=$(echo "$PRS" | jq -r '.[] | select(.labels[]? | test("^release: patch$"; "i")) | .number' | head -1)
          
          # Determine version bump type
          if [ ! -z "$HAS_MAJOR" ]; then
            BUMP_TYPE="major"
            NEW_VERSION="$((MAJOR + 1)).0.0"
          elif [ ! -z "$HAS_MINOR" ]; then
            BUMP_TYPE="minor"
            NEW_VERSION="$MAJOR.$((MINOR + 1)).0"
          elif [ ! -z "$HAS_PATCH" ]; then
            BUMP_TYPE="patch"
            NEW_VERSION="$MAJOR.$MINOR.$((PATCH + 1))"
          else
            BUMP_TYPE="none"
            NEW_VERSION="$CURRENT_VERSION"
          fi
          
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "bump_type=$BUMP_TYPE" >> $GITHUB_OUTPUT
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          echo "skip_reason=" >> $GITHUB_OUTPUT
          
          echo "Bump type: $BUMP_TYPE"
          echo "New version: $NEW_VERSION"

      - name: Generate changelog
        id: generate_changelog
        if: steps.calc_version.outputs.bump_type != 'none'
        run: |
          # Create enhanced changelog generation script
          cat > generate_changelog.js << 'EOF'
          const fs = require('fs');
          
          try {
            const prs = JSON.parse(process.env.PRS_DATA || '[]');
            const currentVersion = process.env.CURRENT_VERSION;
            const newVersion = process.env.NEW_VERSION;
            const bumpType = process.env.BUMP_TYPE;
            
            console.log(`Generating changelog for ${prs.length} PRs`);
            
            // Helper function to check for specific release labels (strict matching)
            const hasReleaseLabel = (pr, releaseType) => {
              if (!pr.labels || !Array.isArray(pr.labels)) return false;
              return pr.labels.some(label => 
                label.toLowerCase() === `release: ${releaseType.toLowerCase()}`
              );
            };
            
            // Helper function to check for skip labels
            const hasSkipLabel = (pr) => {
              if (!pr.labels || !Array.isArray(pr.labels)) return false;
              return pr.labels.some(label => 
                ['release: skip', 'skip-changelog', 'no-release'].includes(label.toLowerCase())
              );
            };
            
            // Strict label-based categorization using ONLY "release:xxx" convention
            const breaking = prs.filter(pr => hasReleaseLabel(pr, 'major'));
            const features = prs.filter(pr => hasReleaseLabel(pr, 'minor'));  
            const fixes = prs.filter(pr => hasReleaseLabel(pr, 'patch'));
            
            // All other PRs (without proper release labels) go to others, except skipped ones
            const others = prs.filter(pr => 
              !hasReleaseLabel(pr, 'major') && 
              !hasReleaseLabel(pr, 'minor') && 
              !hasReleaseLabel(pr, 'patch') &&
              !hasSkipLabel(pr)
            );
            
            // Generate changelog content
            let changelog = `# Release ${newVersion}\n\n`;
            changelog += `**Release Date:** ${new Date().toISOString().split('T')[0]}\n`;
            changelog += `**Version Bump:** ${bumpType}\n\n`;
            
            if (breaking.length > 0) {
              changelog += `## 🚨 BREAKING CHANGES\n\n`;
              breaking.forEach(pr => {
                changelog += `- **#${pr.number}**: ${pr.title} by @${pr.author}\n`;
              });
              changelog += `\n`;
            }
            
            if (features.length > 0) {
              changelog += `## ✨ New Features\n\n`;
              features.forEach(pr => {
                changelog += `- **#${pr.number}**: ${pr.title} by @${pr.author}\n`;
              });
              changelog += `\n`;
            }
            
            if (fixes.length > 0) {
              changelog += `## 🐛 Bug Fixes\n\n`;
              fixes.forEach(pr => {
                changelog += `- **#${pr.number}**: ${pr.title} by @${pr.author}\n`;
              });
              changelog += `\n`;
            }
            

            
            if (others.length > 0) {
              changelog += `## 📝 Other Changes\n\n`;
              others.forEach(pr => {
                changelog += `- **#${pr.number}**: ${pr.title} by @${pr.author}\n`;
              });
              changelog += `\n`;
            }
            
            // Enhanced statistics
            changelog += `## 📊 Release Statistics\n\n`;
            changelog += `- **Total PRs**: ${prs.length}\n`;
            changelog += `- **Contributors**: ${[...new Set(prs.map(pr => pr.author))].length}\n`;
            
            if (breaking.length > 0) changelog += `- **Breaking Changes**: ${breaking.length}\n`;
            if (features.length > 0) changelog += `- **New Features**: ${features.length}\n`;
            if (fixes.length > 0) changelog += `- **Bug Fixes**: ${fixes.length}\n`;
            if (others.length > 0) changelog += `- **Other Changes**: ${others.length}\n`;
            
            // Add contributors list
            const contributors = [...new Set(prs.map(pr => pr.author))];
            if (contributors.length > 0) {
              changelog += `\n### 👥 Contributors\n\n`;
              contributors.forEach(contributor => {
                const prCount = prs.filter(pr => pr.author === contributor).length;
                changelog += `- @${contributor} (${prCount} PR${prCount !== 1 ? 's' : ''})\n`;
              });
            }
            
            // Save changelog
            fs.writeFileSync('generated_changelog.md', changelog);
            console.log('✅ Changelog generated successfully');
            console.log(`📊 Categorization results:`);
            console.log(`   - Breaking: ${breaking.length}`);
            console.log(`   - Features: ${features.length}`);
            console.log(`   - Fixes: ${fixes.length}`);
            console.log(`   - Others: ${others.length}`);
            
          } catch (error) {
            console.error('❌ Error generating changelog:', error.message);
            
            // Create fallback changelog
            const fallbackChangelog = `# Release ${process.env.NEW_VERSION || 'Unknown'}\n\n` +
              `**Release Date:** ${new Date().toISOString().split('T')[0]}\n` +
              `**Version Bump:** ${process.env.BUMP_TYPE || 'unknown'}\n\n` +
              `## ⚠️ Changelog Generation Error\n\n` +
              `Unable to automatically generate detailed changelog.\n` +
              `Please review the included changes manually.\n\n` +
              `**Error:** ${error.message}\n`;
            
            fs.writeFileSync('generated_changelog.md', fallbackChangelog);
            console.log('📝 Fallback changelog created');
          }
          EOF
          
          # Run changelog generation
          node generate_changelog.js
          
          # Validate changelog was created
          if [ ! -f "generated_changelog.md" ]; then
            echo "❌ Error: Changelog file was not created"
            exit 1
          fi
          
          # Check if changelog has content
          if [ ! -s "generated_changelog.md" ]; then
            echo "❌ Error: Changelog file is empty"
            exit 1
          fi
          
          echo "📋 Generated changelog preview:"
          echo "================================"
          head -20 generated_changelog.md
          echo "================================"
          echo "... (showing first 20 lines)"
          
          # Read generated changelog and set output using heredoc
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          cat generated_changelog.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
        env:
          PRS_DATA: ${{ steps.get_prs.outputs.prs }}
          CURRENT_VERSION: ${{ steps.calc_version.outputs.current_version }}
          NEW_VERSION: ${{ steps.calc_version.outputs.version }}
          BUMP_TYPE: ${{ steps.calc_version.outputs.bump_type }}

      - name: Update package.json version
        if: steps.calc_version.outputs.bump_type != 'none' && !inputs.dry_run
        run: |
          # Update package.json version
          npm version ${{ steps.calc_version.outputs.version }} --no-git-tag-version
          
          # Commit version update
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add package.json
          git commit -m "chore: bump version to ${{ steps.calc_version.outputs.version }}"
          git push

      - name: Create release directory and file
        if: steps.calc_version.outputs.bump_type != 'none' && !inputs.dry_run
        run: |
          # Create releases directory if it doesn't exist
          mkdir -p releases
          
          # Create release file
          cat > "releases/${{ steps.calc_version.outputs.version }}.md" << 'EOF'
          ${{ steps.generate_changelog.outputs.changelog }}
          EOF
          
          # Add to git
          git add releases/
          git commit -m "docs: add release ${{ steps.calc_version.outputs.version }}"
          git push

      - name: Create GitHub release
        if: steps.calc_version.outputs.bump_type != 'none' && !inputs.dry_run
        run: |
          # Create GitHub release with notes from file to avoid command line length issues
          echo "${{ steps.generate_changelog.outputs.changelog }}" > release_notes.md
          
          gh release create "v${{ steps.calc_version.outputs.version }}" \
            --title "Release ${{ steps.calc_version.outputs.version }}" \
            --notes-file release_notes.md \
            --target develop
            
          echo "✅ GitHub release v${{ steps.calc_version.outputs.version }} created successfully"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Dry run output
        if: inputs.dry_run && steps.calc_version.outputs.bump_type != 'none'
        run: |
          echo "🔍 DRY RUN MODE - No actual changes will be made"
          echo ""
          echo "📋 Planned Actions:"
          echo "- Update package.json version to: ${{ steps.calc_version.outputs.version }}"
          echo "- Create release file: releases/${{ steps.calc_version.outputs.version }}.md"
          echo "- Create GitHub release: v${{ steps.calc_version.outputs.version }}"
          echo ""
          echo "📄 Generated Changelog:"
          echo "${{ steps.generate_changelog.outputs.changelog }}"

      - name: Summary
        run: |
          echo "## Semantic Release Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by**: Production deployment completion" >> $GITHUB_STEP_SUMMARY
          echo "- **Current Version**: ${{ steps.calc_version.outputs.current_version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **New Version**: ${{ steps.calc_version.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Bump Type**: ${{ steps.calc_version.outputs.bump_type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **PRs Analyzed**: $(echo '${{ steps.get_prs.outputs.prs }}' | jq length)" >> $GITHUB_STEP_SUMMARY
          echo "- **Dry Run**: ${{ inputs.dry_run || 'false' }}" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ steps.calc_version.outputs.skip_reason }}" = "all_prs_skipped" ]; then
            echo "- **Result**: ⏭️ Release skipped - all PRs have skip labels" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.calc_version.outputs.bump_type }}" = "none" ]; then
            echo "- **Result**: No version bump needed" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ inputs.dry_run }}" = "true" ]; then
            echo "- **Result**: Dry run completed - no changes made" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Result**: Release created successfully" >> $GITHUB_STEP_SUMMARY
          fi 