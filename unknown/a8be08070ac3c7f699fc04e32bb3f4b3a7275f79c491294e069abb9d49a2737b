---
description: 
globs: 
alwaysApply: false
---
# Testing Standards

## Objective
Establish comprehensive testing patterns that ensure code reliability, maintainability, and confidence in refactoring.

## Context
- React Testing Library for component testing
- Jest as the test runner and assertion library
- Material-UI component testing patterns
- Custom hook testing strategies
- Mocking external dependencies and APIs

## Rules

### Test File Organization
- **Test File Naming**: Co-locate tests with source files
  ```
  // ✅ Good
  src/
    components/
      user-profile/
        user-profile.tsx
        user-profile.test.tsx
        index.ts
    hooks/
      use-auth-context.ts
      use-auth-context.test.ts
  
  // ❌ Avoid separate test directories
  src/
    components/user-profile.tsx
    __tests__/user-profile.test.tsx
  ```

- **Test Suite Structure**: Organize tests with descriptive describe blocks
  ```typescript
  // ✅ Good
  describe('UserProfile', () => {
    describe('when user is authenticated', () => {
      it('should display user name and email', () => {
        // Test implementation
      });
      
      it('should show edit button when user can edit', () => {
        // Test implementation
      });
    });
    
    describe('when user is not authenticated', () => {
      it('should display login prompt', () => {
        // Test implementation
      });
    });
    
    describe('interactions', () => {
      it('should call onEdit when edit button is clicked', () => {
        // Test implementation
      });
    });
  });
  ```

### Component Testing Patterns
- **Setup and Teardown**: Use proper test utilities
  ```typescript
  // ✅ Good
  import { render, screen, fireEvent, waitFor } from '@testing-library/react';
  import userEvent from '@testing-library/user-event';
  import { TestWrapper } from 'src/test-utils';
  
  const renderUserProfile = (props: Partial<UserProfileProps> = {}) => {
    const defaultProps: UserProfileProps = {
      user: mockUser,
      onEdit: jest.fn(),
      disabled: false,
    };
    
    return render(
      <TestWrapper>
        <UserProfile {...defaultProps} {...props} />
      </TestWrapper>
    );
  };
  
  describe('UserProfile', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    it('should render user information', () => {
      renderUserProfile();
      
      expect(screen.getByText(mockUser.name)).toBeInTheDocument();
      expect(screen.getByText(mockUser.email)).toBeInTheDocument();
    });
  });
  ```

- **User Interactions**: Test user actions over implementation details
  ```typescript
  // ✅ Good
  it('should call onEdit when edit button is clicked', async () => {
    const mockOnEdit = jest.fn();
    const user = userEvent.setup();
    
    renderUserProfile({ onEdit: mockOnEdit });
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    await user.click(editButton);
    
    expect(mockOnEdit).toHaveBeenCalledWith(mockUser);
  });
  
  // ❌ Avoid testing implementation details
  it('should set state when edit button is clicked', () => {
    // Testing internal state instead of behavior
  });
  ```

- **Async Testing**: Handle asynchronous operations properly
  ```typescript
  // ✅ Good
  it('should display loading state while fetching data', async () => {
    const mockFetch = jest.fn().mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockData), 100))
    );
    
    renderComponent({ fetchData: mockFetch });
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText(mockData.title)).toBeInTheDocument();
    });
    
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
  });
  ```

### Custom Hook Testing
- **Hook Testing Setup**: Use renderHook for custom hooks
  ```typescript
  // ✅ Good
  import { renderHook, act } from '@testing-library/react';
  
  describe('useAuthContext', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );
    
    it('should throw error when used outside AuthProvider', () => {
      // Suppress console.error for this test
      jest.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        renderHook(() => useAuthContext());
      }).toThrow('useAuthContext must be used within AuthProvider');
      
      (console.error as jest.Mock).mockRestore();
    });
    
    it('should provide authentication methods', () => {
      const { result } = renderHook(() => useAuthContext(), { wrapper });
      
      expect(result.current.login).toBeDefined();
      expect(result.current.logout).toBeDefined();
      expect(result.current.isAuthenticated).toBe(false);
    });
    
    it('should update authentication state on login', async () => {
      const { result } = renderHook(() => useAuthContext(), { wrapper });
      
      await act(async () => {
        await result.current.login({ email: '<EMAIL>', password: 'password' });
      });
      
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toBeDefined();
    });
  });
  ```

### Mocking Strategies
- **API Mocking**: Mock external API calls consistently
  ```typescript
  // ✅ Good
  import { rest } from 'msw';
  import { setupServer } from 'msw/node';
  
  const server = setupServer(
    rest.get('/api/users/:id', (req, res, ctx) => {
      return res(
        ctx.json({
          id: req.params.id,
          name: 'John Doe',
          email: '<EMAIL>',
        })
      );
    })
  );
  
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());
  ```

- **Module Mocking**: Mock complex dependencies
  ```typescript
  // ✅ Good
  jest.mock('src/hooks/use-auth-context', () => ({
    useAuthContext: () => ({
      user: mockUser,
      isAuthenticated: true,
      login: jest.fn(),
      logout: jest.fn(),
    }),
  }));
  
  // For specific test cases
  const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
  
  it('should handle unauthenticated state', () => {
    mockUseAuthContext.mockReturnValue({
      user: null,
      isAuthenticated: false,
      login: jest.fn(),
      logout: jest.fn(),
    });
    
    renderComponent();
    
    expect(screen.getByText(/please log in/i)).toBeInTheDocument();
  });
  ```

### Accessibility Testing
- **A11y Assertions**: Include accessibility testing
  ```typescript
  // ✅ Good
  import { axe, toHaveNoViolations } from 'jest-axe';
  
  expect.extend(toHaveNoViolations);
  
  it('should not have accessibility violations', async () => {
    const { container } = renderUserProfile();
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  
  it('should have proper ARIA labels', () => {
    renderUserProfile();
    
    const editButton = screen.getByRole('button', { name: /edit user profile/i });
    expect(editButton).toBeInTheDocument();
    
    const avatar = screen.getByRole('img', { name: /user avatar/i });
    expect(avatar).toBeInTheDocument();
  });
  ```

### Redux Testing
- **Store Testing**: Test Redux integration
  ```typescript
  // ✅ Good
  import { renderWithProviders } from 'src/test-utils';
  import { store } from 'src/store';
  
  const renderWithStore = (
    component: React.ReactElement,
    initialState?: Partial<RootState>
  ) => {
    return renderWithProviders(component, {
      preloadedState: initialState,
    });
  };
  
  it('should dispatch action when button is clicked', async () => {
    const user = userEvent.setup();
    
    renderWithStore(<ConnectedComponent />);
    
    const button = screen.getByRole('button', { name: /save/i });
    await user.click(button);
    
    const state = store.getState();
    expect(state.user.isLoading).toBe(true);
  });
  ```

### Error Boundary Testing
- **Error Handling**: Test error boundaries and error states
  ```typescript
  // ✅ Good
  const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
    if (shouldThrow) {
      throw new Error('Test error');
    }
    return <div>No error</div>;
  };
  
  it('should catch and display errors', () => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    expect(screen.queryByText('No error')).not.toBeInTheDocument();
    
    (console.error as jest.Mock).mockRestore();
  });
  ```

### Test Utilities
- **Custom Render Functions**: Create reusable test utilities
  ```typescript
  // ✅ Good - test-utils.ts
  import { render } from '@testing-library/react';
  import { Provider } from 'react-redux';
  import { ThemeProvider } from '@mui/material/styles';
  import { configureStore } from '@reduxjs/toolkit';
  
  interface RenderOptions {
    preloadedState?: Partial<RootState>;
    theme?: Theme;
  }
  
  export const renderWithProviders = (
    ui: React.ReactElement,
    options: RenderOptions = {}
  ) => {
    const { preloadedState, theme = defaultTheme } = options;
    
    const store = configureStore({
      reducer: rootReducer,
      preloadedState,
    });
    
    const TestWrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </Provider>
    );
    
    return render(ui, { wrapper: TestWrapper });
  };
  
  // Mock data factories
  export const createMockUser = (overrides: Partial<User> = {}): User => ({
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'user',
    ...overrides,
  });
  ```

### Performance Testing
- **Rendering Performance**: Test for unnecessary re-renders
  ```typescript
  // ✅ Good
  it('should not re-render when props do not change', () => {
    const renderSpy = jest.fn();
    const MemoizedComponent = React.memo(() => {
      renderSpy();
      return <div>Component</div>;
    });
    
    const { rerender } = render(<MemoizedComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1);
    
    rerender(<MemoizedComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1); // Should not re-render
  });
  ```

## Test Coverage Standards
- **Minimum Coverage**: Aim for 80% overall coverage
- **Critical Paths**: 100% coverage for authentication, payment, and security features
- **Component Coverage**: Test all public props and user interactions
- **Hook Coverage**: Test all return values and state changes

## Exceptions
- **Third-party component wrappers**: Minimal testing when wrapping external libraries
- **Simple presentational components**: Basic smoke tests may be sufficient
- **Legacy code**: Maintain existing test patterns during incremental migration
