import type { RouteObject } from 'react-router';

import { lazy, Suspense } from 'react';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const DownloadCaptionPage = lazy(() => import('src/pages/download/download-caption'));

// ----------------------------------------------------------------------

export const downloadRoutes: RouteObject[] = [
  {
    path: 'download-caption/:resourceId',
    element: (
      <AuthGuard>
        <Suspense fallback={<LoadingScreen />}>
          <DownloadCaptionPage />
        </Suspense>
      </AuthGuard>
    ),
  },
];
