import type { BoxProps } from '@mui/material/Box';

import Box from '@mui/material/Box';
import Radio from '@mui/material/Radio';
import { styled } from '@mui/material/styles';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type RadioOptionProps = BoxProps & {
  value: string;
  label: string;
  icon?: string;
  selected: boolean;
  onChange: (value: string) => void;
};

const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  margin: 0,
  '& .MuiFormControlLabel-label': {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    fontSize: theme.typography.pxToRem(14),
  },
}));

export function RadioOption({
  value,
  label,
  icon,
  selected,
  onChange,
  sx,
  ...other
}: RadioOptionProps) {
  return (
    <Box sx={sx} {...other}>
      <StyledFormControlLabel
        control={
          <Radio checked={selected} onChange={() => onChange(value)} value={value} size="small" />
        }
        label={
          <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon && <Iconify icon={icon} width={20} height={20} />}
            {label}
          </Box>
        }
      />
    </Box>
  );
}
