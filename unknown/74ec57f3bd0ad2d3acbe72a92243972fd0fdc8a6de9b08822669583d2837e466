import type { Resource } from 'src/types';
import type { ApiRequestConfig } from 'src/store/api/types';

import type { ConvertDownloadFormat } from './types';

export interface ResourceApiConfigs {
  getResources: ApiRequestConfig<{}>;
  getResource: ApiRequestConfig<{ id: string }>;
  getResourceUploadUrl: ApiRequestConfig<{
    payload: {
      fileName: string;
      projectId?: string;
    };
  }>;
  createResource: ApiRequestConfig<{
    payload: {
      fileName: string;
      fileSize: number;
      fileLastModified: string;
      uploadedFileName: string;
    };
  }>;
  updateResource: ApiRequestConfig<{ id: string; payload: Partial<Resource> }>;
  deleteResource: ApiRequestConfig<{ id: string }>;
  uploadResourceThumbnail: ApiRequestConfig<{
    id: string;
    payload: {
      imageUrl: string;
    };
  }>;
  extendResourceRetention: ApiRequestConfig<{
    id: string;
  }>;
  transcodingStatus: ApiRequestConfig<{
    id: string;
    payload: {
      ids: string[];
    };
  }>;
  convertDownload: ApiRequestConfig<{
    id: string;
    payload: {
      format: ConvertDownloadFormat;
    };
  }>;
}

export type ResourceConfigParams<Config extends keyof ResourceApiConfigs> = Parameters<
  ResourceApiConfigs[Config]
>[0];

export const resourceApiConfigs: ResourceApiConfigs = {
  getResources: () => ({
    method: 'GET',
    uri: 'resources',
  }),
  getResource: (args) => ({
    method: 'GET',
    uri: `resource/${args.id}`,
  }),
  getResourceUploadUrl: (args) => ({
    method: 'POST',
    uri: 'resource/upload-url',
    data: args.payload,
  }),
  createResource: (args) => ({
    method: 'POST',
    uri: 'resource',
    data: args.payload,
  }),
  updateResource: (args) => ({
    method: 'PUT',
    uri: `resource/${args.id}`,
    data: args.payload,
  }),
  deleteResource: (args) => ({
    method: 'DELETE',
    uri: `resource/${args.id}`,
  }),
  uploadResourceThumbnail: (args) => ({
    method: 'PUT',
    uri: `resource/${args.id}/upload-thumbnail`,
    data: args.payload,
  }),
  extendResourceRetention: (args) => ({
    method: 'POST',
    uri: `resource/${args.id}/extend-retention`,
  }),
  transcodingStatus: (args) => ({
    method: 'POST',
    uri: 'resources/transcoding-status',
    data: args.payload,
  }),
  convertDownload: (args) => ({
    method: 'POST',
    uri: `resource/${args.id}/convert-download`,
    data: args.payload,
  }),
};
