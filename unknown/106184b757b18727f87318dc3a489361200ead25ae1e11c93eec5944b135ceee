import { toast } from 'sonner';
import { useState } from 'react';

import { useUploadFile } from 'src/utils/resource';

import { useAppSelector } from 'src/store';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

const useConvertToResource = () => {
  const currentProjectId = useAppSelector(selectLastViewedProjectId);
  const uploadFile = useUploadFile();
  const [isConverting, setIsConverting] = useState(false);

  const convertNoteToResource = async (text: string, fileName = 'Untitled') => {
    try {
      setIsConverting(true);
      // convert text as file
      const file = new File([text], `${fileName}.md`, { type: 'text/markdown' });

      // upload file
      await uploadFile(file, currentProjectId ?? undefined);
      toast.success(`Converted note to resource successfully`);
    } catch (error: any) {
      // Don't show error toast if it's permission denied (already shown by uploadFile)
      if (error?.message !== 'Permission denied') {
        toast.error('Failed to convert note to resource');
      }
    } finally {
      setIsConverting(false);
    }
  };

  const convertTextToResource = async (text: string, fileName = 'Untitled') => {
    try {
      setIsConverting(true);
      // convert text as file
      const file = new File([text], `${fileName}.md`, { type: 'text/markdown' });

      // upload file
      await uploadFile(file, currentProjectId ?? undefined);
      toast.success(`Converted text to resource successfully`);
    } catch (error: any) {
      // Don't show error toast if it's permission denied (already shown by uploadFile)
      if (error?.message !== 'Permission denied') {
        toast.error('Failed to convert text to resource');
      }
    } finally {
      setIsConverting(false);
    }
  };

  return {
    convertNoteToResource,
    convertTextToResource,
    isConverting,
  };
};

export default useConvertToResource;
