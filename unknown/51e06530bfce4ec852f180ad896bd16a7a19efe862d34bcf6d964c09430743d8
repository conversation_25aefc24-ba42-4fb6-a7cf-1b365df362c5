import { toast } from 'sonner';
import { useState } from 'react';
import FileSaver from 'file-saver';

import useAnalytics from 'src/hooks/analytics';

import { getDisplayFileName } from 'src/utils/file-utils';

import axiosInstance from 'src/lib/axios';
import { resourceApiConfigs, ConvertDownloadFormat } from 'src/store/api/resources';

import type { ResourceItem } from '../components/resources-list';

interface UseDownloadSourceReturn {
  downloadSource: (
    resource: ResourceItem,
    format?: ConvertDownloadFormat
  ) => Promise<void>;
  isDownloading: ConvertDownloadFormat | null;
}

/**
 * Custom hook for downloading source files with format conversion
 */
const useDownloadSource = (): UseDownloadSourceReturn => {
  const { trackEvent } = useAnalytics();
  const [isDownloading, setIsDownloading] = useState<ConvertDownloadFormat | null>(null);

  const downloadSource = async (
    resource: ResourceItem,
    format?: ConvertDownloadFormat
  ): Promise<void> => {
    if (!resource.id) {
      toast.error('Invalid resource', {
        description: 'Resource ID is missing',
      });
      return;
    }

    const formatSuffix = format ? ` as ${format.toUpperCase()}` : '';

    // Track download start event
    trackEvent({
      eventCategory: 'Resource',
      eventAction: `Download source${formatSuffix}`,
      properties: {
        resourceId: resource.id,
        resourceName: resource.name,
        format: format || 'original',
      },
    });

    // Show download started toast
    toast.success(`Download started for ${resource.name}${formatSuffix}`, {
      description: 'Please wait while the file is being prepared...',
      duration: 3000,
    });

    setIsDownloading(format || ConvertDownloadFormat.Txt);

    try {
      const config = resourceApiConfigs.convertDownload({
        id: resource.id,
        payload: {
          format: format || ConvertDownloadFormat.Txt,
        },
      });
      const response = await axiosInstance.request({
        ...config,
        url: config.uri,
        responseType: 'blob',
      });

      // Download the file using FileSaver
      const fileName = format ? `${getDisplayFileName(resource.name)}.${format}` : resource.name;
      FileSaver.saveAs(response.data, fileName);

      // Show completion toast
      toast.success(`Download completed for ${resource.name}${formatSuffix}`, {
        description: 'File has been saved to your device',
        duration: 5000,
      });
    } catch (error: any) {
      // Improved error handling with specific error types
      const isNetworkError = !error.response;
      const isServerError = error.response?.status >= 500;
      const isClientError = error.response?.status >= 400 && error.response?.status < 500;

      const errorMessage = `Failed to download ${resource.name}${formatSuffix}`;
      let errorDescription = 'Please try again later';

      if (isNetworkError) {
        errorDescription = 'Check your internet connection and try again';
      } else if (isServerError) {
        errorDescription = 'Server error. Please try again in a few minutes';
      } else if (isClientError) {
        errorDescription = 'File may no longer be available or you lack permission';
      }

      // Enhanced logging for debugging
      console.error('Download failed:', {
        resourceId: resource.id,
        resourceName: resource.name,
        format: format || 'original',
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });

      toast.error(errorMessage, {
        description: errorDescription,
      });

      // Track error event for analytics
      trackEvent({
        eventCategory: 'Resource',
        eventAction: 'Download source failed',
        properties: {
          resourceId: resource.id,
          resourceName: resource.name,
          format: format || 'original',
          errorType: isNetworkError ? 'network' : isServerError ? 'server' : 'client',
          statusCode: error.response?.status,
        },
      });
    } finally {
      setIsDownloading(null);
    }
  };

  return {
    downloadSource,
    isDownloading,
  };
};

export default useDownloadSource; 