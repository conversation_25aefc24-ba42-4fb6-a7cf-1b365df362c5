---
description: 
globs: 
alwaysApply: false
---
# State Management Best Practices

## Objective
Establish consistent patterns for Redux Toolkit, RTK Query, and React state management to ensure predictable and maintainable application state.

## Context
- Redux Toolkit for global state management
- RTK Query for server state and API interactions
- React contexts for component-level shared state
- Custom hooks for stateful logic encapsulation
- GraphQL with RTK Query integration

## Rules

### Redux Toolkit Slice Patterns
- **Slice Definition**: Use createSlice with proper typing
  ```typescript
  // ✅ Good
  interface UserState {
    currentUser: User | null;
    isLoading: boolean;
    error: string | null;
  }
  
  const initialState: UserState = {
    currentUser: null,
    isLoading: false,
    error: null,
  };
  
  const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
      setUser: (state, action: PayloadAction<User>) => {
        state.currentUser = action.payload;
        state.error = null;
      },
      setLoading: (state, action: PayloadAction<boolean>) => {
        state.isLoading = action.payload;
      },
      setError: (state, action: PayloadAction<string>) => {
        state.error = action.payload;
        state.isLoading = false;
      },
      clearUser: (state) => {
        state.currentUser = null;
        state.error = null;
      },
    },
  });
  ```

- **Action Creators**: Export both actions and selectors
  ```typescript
  // ✅ Good
  export const { setUser, setLoading, setError, clearUser } = userSlice.actions;
  
  // Selectors
  export const selectCurrentUser = (state: RootState) => state.user.currentUser;
  export const selectUserLoading = (state: RootState) => state.user.isLoading;
  export const selectUserError = (state: RootState) => state.user.error;
  
  export default userSlice.reducer;
  ```

- **Async Thunks**: Use createAsyncThunk for side effects
  ```typescript
  // ✅ Good
  export const fetchUserProfile = createAsyncThunk(
    'user/fetchProfile',
    async (userId: string, { rejectWithValue }) => {
      try {
        const response = await userApi.getProfile(userId);
        return response.data;
      } catch (error) {
        return rejectWithValue(
          error instanceof Error ? error.message : 'Failed to fetch user'
        );
      }
    }
  );
  
  // Handle in extraReducers
  const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: { /* ... */ },
    extraReducers: (builder) => {
      builder
        .addCase(fetchUserProfile.pending, (state) => {
          state.isLoading = true;
          state.error = null;
        })
        .addCase(fetchUserProfile.fulfilled, (state, action) => {
          state.currentUser = action.payload;
          state.isLoading = false;
        })
        .addCase(fetchUserProfile.rejected, (state, action) => {
          state.error = action.payload as string;
          state.isLoading = false;
        });
    },
  });
  ```

### RTK Query API Patterns
- **API Definition**: Use createApi with proper endpoints
  ```typescript
  // ✅ Good
  export const projectsApi = createApi({
    reducerPath: 'projectsApi',
    baseQuery: graphqlRequestBaseQuery({
      url: '/graphql',
      prepareHeaders: (headers, { getState }) => {
        const token = (getState() as RootState).auth.token;
        if (token) {
          headers.set('authorization', `Bearer ${token}`);
        }
        return headers;
      },
    }),
    tagTypes: ['Project', 'ProjectMember'],
    endpoints: (builder) => ({
      getProjects: builder.query<Project[], void>({
        query: () => ({
          document: gql`
            query GetProjects {
              projects {
                id
                name
                description
                createdAt
                updatedAt
              }
            }
          `,
        }),
        providesTags: ['Project'],
      }),
      
      createProject: builder.mutation<Project, CreateProjectInput>({
        query: (input) => ({
          document: gql`
            mutation CreateProject($input: CreateProjectInput!) {
              createProject(input: $input) {
                id
                name
                description
              }
            }
          `,
          variables: { input },
        }),
        invalidatesTags: ['Project'],
      }),
    }),
  });
  
  export const {
    useGetProjectsQuery,
    useCreateProjectMutation,
  } = projectsApi;
  ```

- **Query Hooks Usage**: Proper error handling and loading states
  ```typescript
  // ✅ Good
  const ProjectList = () => {
    const {
      data: projects,
      isLoading,
      isError,
      error,
      refetch,
    } = useGetProjectsQuery();
    
    const [createProject, { isLoading: isCreating }] = useCreateProjectMutation();
    
    const handleCreateProject = async (input: CreateProjectInput) => {
      try {
        await createProject(input).unwrap();
        // Success handling
      } catch (error) {
        // Error handling
        console.error('Failed to create project:', error);
      }
    };
    
    if (isLoading) return <LoadingSpinner />;
    if (isError) return <ErrorMessage error={error} onRetry={refetch} />;
    
    return (
      <div>
        {projects?.map(project => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>
    );
  };
  ```

### React Context Patterns
- **Context Definition**: Type-safe context creation
  ```typescript
  // ✅ Good
  interface AuthContextType {
    user: User | null;
    isAuthenticated: boolean;
    login: (credentials: LoginCredentials) => Promise<void>;
    logout: () => void;
    loading: boolean;
  }
  
  const AuthContext = createContext<AuthContextType | undefined>(undefined);
  
  export const useAuthContext = () => {
    const context = useContext(AuthContext);
    if (!context) {
      throw new Error('useAuthContext must be used within AuthProvider');
    }
    return context;
  };
  
  export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(false);
    
    const login = async (credentials: LoginCredentials) => {
      setLoading(true);
      try {
        const user = await authService.login(credentials);
        setUser(user);
      } finally {
        setLoading(false);
      }
    };
    
    const logout = () => {
      setUser(null);
      authService.logout();
    };
    
    const value = {
      user,
      isAuthenticated: !!user,
      login,
      logout,
      loading,
    };
    
    return (
      <AuthContext.Provider value={value}>
        {children}
      </AuthContext.Provider>
    );
  };
  ```

### Custom Hooks Patterns
- **Stateful Logic Encapsulation**: Extract complex state logic
  ```typescript
  // ✅ Good
  interface UseFormReturn<T> {
    values: T;
    errors: Record<keyof T, string>;
    touched: Record<keyof T, boolean>;
    setValue: (field: keyof T, value: any) => void;
    setError: (field: keyof T, error: string) => void;
    handleSubmit: (onSubmit: (values: T) => void) => React.FormEventHandler;
    reset: () => void;
  }
  
  export function useForm<T extends Record<string, any>>(
    initialValues: T,
    validate?: (values: T) => Record<keyof T, string>
  ): UseFormReturn<T> {
    const [values, setValues] = useState<T>(initialValues);
    const [errors, setErrors] = useState<Record<keyof T, string>>({} as Record<keyof T, string>);
    const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
    
    const setValue = useCallback((field: keyof T, value: any) => {
      setValues(prev => ({ ...prev, [field]: value }));
      setTouched(prev => ({ ...prev, [field]: true }));
    }, []);
    
    const setError = useCallback((field: keyof T, error: string) => {
      setErrors(prev => ({ ...prev, [field]: error }));
    }, []);
    
    const handleSubmit = useCallback((onSubmit: (values: T) => void) => {
      return (event: React.FormEvent) => {
        event.preventDefault();
        
        if (validate) {
          const validationErrors = validate(values);
          if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
          }
        }
        
        onSubmit(values);
      };
    }, [values, validate]);
    
    const reset = useCallback(() => {
      setValues(initialValues);
      setErrors({} as Record<keyof T, string>);
      setTouched({} as Record<keyof T, boolean>);
    }, [initialValues]);
    
    return {
      values,
      errors,
      touched,
      setValue,
      setError,
      handleSubmit,
      reset,
    };
  }
  ```

### Store Configuration
- **Store Setup**: Proper middleware and dev tools configuration
  ```typescript
  // ✅ Good
  import { configureStore } from '@reduxjs/toolkit';
  import { persistStore, persistReducer } from 'redux-persist';
  import storage from 'redux-persist/lib/storage';
  
  import { authSlice } from './slices/auth-slice';
  import { projectsApi } from './api/projects-api';
  
  const persistConfig = {
    key: 'root',
    storage,
    whitelist: ['auth'], // Only persist auth state
  };
  
  const persistedReducer = persistReducer(persistConfig, authSlice.reducer);
  
  export const store = configureStore({
    reducer: {
      auth: persistedReducer,
      [projectsApi.reducerPath]: projectsApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        },
      }).concat(projectsApi.middleware),
    devTools: process.env.NODE_ENV !== 'production',
  });
  
  export const persistor = persistStore(store);
  export type RootState = ReturnType<typeof store.getState>;
  export type AppDispatch = typeof store.dispatch;
  ```

### State Selection Patterns
- **Memoized Selectors**: Use reselect for computed state
  ```typescript
  // ✅ Good
  import { createSelector } from '@reduxjs/toolkit';
  
  const selectProjects = (state: RootState) => state.projects.items;
  const selectProjectFilters = (state: RootState) => state.projects.filters;
  
  export const selectFilteredProjects = createSelector(
    [selectProjects, selectProjectFilters],
    (projects, filters) => {
      return projects.filter(project => {
        if (filters.status && project.status !== filters.status) return false;
        if (filters.search && !project.name.toLowerCase().includes(filters.search.toLowerCase())) return false;
        return true;
      });
    }
  );
  ```

### Error Handling Patterns
- **Consistent Error States**: Handle errors uniformly
  ```typescript
  // ✅ Good
  interface AsyncState<T> {
    data: T | null;
    loading: boolean;
    error: string | null;
  }
  
  const createAsyncState = <T>(): AsyncState<T> => ({
    data: null,
    loading: false,
    error: null,
  });
  
  // In slice
  const handleAsyncPending = <T>(state: { async: AsyncState<T> }) => {
    state.async.loading = true;
    state.async.error = null;
  };
  
  const handleAsyncFulfilled = <T>(state: { async: AsyncState<T> }, action: PayloadAction<T>) => {
    state.async.data = action.payload;
    state.async.loading = false;
    state.async.error = null;
  };
  
  const handleAsyncRejected = <T>(state: { async: AsyncState<T> }, action: PayloadAction<string | undefined>) => {
    state.async.loading = false;
    state.async.error = action.payload || 'An error occurred';
  };
  ```

## Exceptions
- **Performance critical paths**: Direct state updates for high-frequency updates
- **Third-party integrations**: Alternative state patterns for external library requirements
- **Legacy modules**: Maintain consistency within existing state management approaches
