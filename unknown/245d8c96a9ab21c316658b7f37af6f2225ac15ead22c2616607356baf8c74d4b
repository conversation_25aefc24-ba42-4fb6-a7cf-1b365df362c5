import type { Theme, SxProps } from '@mui/material/styles';
import type { ButtonBaseProps } from '@mui/material/ButtonBase';
import type { UserProject, UserInitialContext } from 'src/types';

import Box from '@mui/material/Box';
import { Stack } from '@mui/material';
import ButtonBase from '@mui/material/ButtonBase';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import { SvgColor } from 'src/components/svg-color';

// ----------------------------------------------------------------------

export type ProjectPopoverProps = ButtonBaseProps & {
  data?: UserInitialContext['projects'];
  currentProject: UserProject;
  showAsLink?: boolean;
};

export function ProjectPopover({
  data = [],
  currentProject: project,
  showAsLink = false,
  sx,
  ...other
}: ProjectPopoverProps) {
  const buttonBg: SxProps<Theme> = {
    height: 1,
    zIndex: -1,
    opacity: 0,
    content: "''",
    borderRadius: 1,
    position: 'absolute',
    visibility: 'hidden',
    bgcolor: 'action.hover',
    width: 'calc(100% + 8px)',
    transition: (theme) =>
      theme.transitions.create(['opacity', 'visibility'], {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.shorter,
      }),
  };

  return (
    <Stack direction="row" alignItems="center" gap={1}>
      <ButtonBase
        sx={[
          {
            p: 1,
            gap: { xs: 0.5, sm: 1 },
            '&::before': buttonBg,
            '&:hover': {
              bgcolor: 'action.hover',
            },
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        <SvgColor
          color="primary"
          src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-folder.svg`}
          sx={{ width: 24, height: 24, color: 'primary.main' }}
        />

        <Box
          component="span"
          sx={{ typography: 'subtitle2', display: { xs: 'none', sm: 'inline-flex' } }}
        >
          {project?.name}
        </Box>

        {!showAsLink && (
          <Iconify width={16} icon="carbon:chevron-sort" sx={{ color: 'text.disabled' }} />
        )}
      </ButtonBase>
    </Stack>
  );
}
