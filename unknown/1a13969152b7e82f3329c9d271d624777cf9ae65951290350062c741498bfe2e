import type { SxProps } from '@mui/material';
import type { IMode } from 'src/types/ai-suggestions';
import type { LiveTranscriptionSegment } from 'src/types';

import { formatDate } from 'date-fns';
import { useRef, useState, useEffect, useCallback } from 'react';

import { Box, Fade, Paper, Stack, Divider, Typography } from '@mui/material';

import { useAiSuggestions } from 'src/hooks/ai-suggestion';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

import SelectionButtons from './SelectionButtons';

interface AidaSuggestionsPanelProps {
  transcriptions: LiveTranscriptionSegment[];
  recordingCompleted: boolean;
  sx?: SxProps;
}

interface Suggestion {
  text: string;
  timestamp: Date;
}

const AidaSuggestionsPanel: React.FC<AidaSuggestionsPanelProps> = ({ transcriptions, recordingCompleted, sx = {} }) => {
  const { followUpQuestions, handleTranscriptsChange, isLoading } = useAiSuggestions();
  const [selectedMode, setSelectedMode] = useState<IMode>('balanced');
  const [questionHistory, setQuestionHistory] = useState<Suggestion[]>([]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [interactionTimeout, setInteractionTimeout] = useState<NodeJS.Timeout | null>(null);

  // Handle user interaction detection
  const handleUserInteraction = useCallback(() => {
    setIsUserInteracting(true);
    
    // Clear existing timeout
    if (interactionTimeout) {
      clearTimeout(interactionTimeout);
    }
    
    // Set new timeout to resume auto-scroll after 3 seconds of no interaction
    const timeout = setTimeout(() => {
      setIsUserInteracting(false);
    }, 3000);
    
    setInteractionTimeout(timeout);
  }, [interactionTimeout]);

  // Cleanup timeout on unmount
  useEffect(() => () => {
      if (interactionTimeout) {
        clearTimeout(interactionTimeout);
      }
    }, [interactionTimeout]);

  useEffect(() => {
    if (transcriptions?.length && !recordingCompleted) {
      handleTranscriptsChange(transcriptions, selectedMode);
    }
  }, [transcriptions, selectedMode, handleTranscriptsChange, recordingCompleted]);

  useEffect(() => {
    if (followUpQuestions?.length) {
      // Filter out error messages and empty questions
      const validNewQuestions = followUpQuestions.filter(
        (question) =>
          question &&
          !question.includes('Failed to generate') &&
          !question.includes('Please try again')
      );

      // Add new questions to history, avoiding duplicates
      setQuestionHistory((prev) => {
        const newQuestions = validNewQuestions
          .filter((newQ) => !prev.some((existingQ) => existingQ.text === newQ))
          .map((text) => ({
            text,
            timestamp: new Date(),
          }));
        return [...prev, ...newQuestions];
      });
    }
  }, [followUpQuestions]);

  // Auto-scroll to bottom when new questions are added (only if user is not interacting)
  useEffect(() => {
    if (scrollContainerRef.current && questionHistory.length > 0 && !isUserInteracting) {
      const scrollElement = scrollContainerRef.current.querySelector('[data-simplebar-content]') || 
                           scrollContainerRef.current;
      
      // Use requestAnimationFrame to ensure DOM updates are complete
      requestAnimationFrame(() => {
        scrollElement.scrollTo({
          top: scrollElement.scrollHeight,
          behavior: 'smooth'
        });
      });
    }
  }, [questionHistory.length, isUserInteracting]); // Trigger on question history length change or interaction state change

  return (
    <Paper
      elevation={1}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '50%',
        },
        ...sx,
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography sx={{ mr: 2 }} variant="subtitle2" color="textPrimary">
            Aida
          </Typography>
        </Stack>
        <Stack direction="row" spacing={1} alignItems="center">
          <SelectionButtons value={selectedMode} onChange={setSelectedMode} />
        </Stack>
      </Stack>

      <Stack sx={{ my: 2, flex: 1, minHeight: 0 }}>
        <Divider sx={{ mb: 2 }} />

        <Scrollbar
          ref={scrollContainerRef}
          sx={{
            flex: 1,
            '& .simplebar-content': {
              display: 'flex',
              flexDirection: 'column',
            },
          }}
          onMouseDown={handleUserInteraction}
          onWheel={handleUserInteraction}
          onTouchStart={handleUserInteraction}
          onScroll={handleUserInteraction}
        >
          {transcriptions?.length > 0 ? (
            <Stack direction="row" spacing={2} alignItems="flex-start" sx={{ mb: 2 }}>
              <SuggestionItem questions={questionHistory} isLoading={isLoading} />
            </Stack>
          ) : (
            <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Start talking to get follow-up questions
              </Typography>
            </Stack>
          )}
        </Scrollbar>
      </Stack>
    </Paper>
  );
};

const SuggestionItem = ({
  questions,
  isLoading,
}: {
  questions: Suggestion[];
  isLoading: boolean;
}) => (
  <Stack direction="row" spacing={2} alignItems="flex-start" sx={{ mb: 2, width: '100%' }}>
    <Box sx={{ width: '100%' }}>
      <Stack spacing={1}>
        {questions.map((q, idx) => (
          <Fade key={idx} in timeout={500} style={{ transitionDelay: `${idx * 100}ms` }}>
            <Paper
              variant="outlined"
              sx={{
                p: 1.5,
                backgroundColor: 'background.paper',
                '&:hover': {
                  backgroundColor: 'action.hover',
                  cursor: 'pointer',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <Stack spacing={0.5}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  {q.text}
                </Typography>
                <Typography variant="caption" color="text.disabled">
                  {formatDate(q.timestamp, 'hh:mm:ss aa')}
                </Typography>
              </Stack>
            </Paper>
          </Fade>
        ))}
        {isLoading && (
          <Paper
            variant="outlined"
            sx={{
              p: 1.5,
              backgroundColor: 'background.paper',
            }}
          >
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Iconify icon="eos-icons:loading" width={16} sx={{ color: 'primary.main' }} />
              Generating suggestions...
            </Typography>
          </Paper>
        )}
      </Stack>
    </Box>
  </Stack>
);

export default AidaSuggestionsPanel;
