import { ProjectRole } from 'src/store/api/projects';

export interface ProjectPermissions {
  canView: boolean;
  canEdit: boolean;
  canComment: boolean;
  canManageMembers: boolean;
  canUploadFiles: boolean;
  canDeleteFiles: boolean;
  canCreateFolders: boolean;
  canDeleteFolders: boolean;
}

export const getProjectPermissions = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false
): ProjectPermissions => {
  // Owner has all permissions
  if (isOwner) {
    return {
      canView: true,
      canEdit: true,
      canComment: true,
      canManageMembers: true,
      canUploadFiles: true,
      canDeleteFiles: true,
      canCreateFolders: true,
      canDeleteFolders: true,
    };
  }

  switch (role) {
    case ProjectRole.EDITOR:
      return {
        canView: true,
        canEdit: true,
        canComment: true,
        canManageMembers: false,
        canUploadFiles: true,
        canDeleteFiles: true,
        canCreateFolders: true,
        canDeleteFolders: true,
      };

    case ProjectRole.COMMENTER:
      return {
        canView: true,
        canEdit: false,
        canComment: true,
        canManageMembers: false,
        canUploadFiles: false,
        canDeleteFiles: false,
        canCreateFolders: false,
        canDeleteFolders: false,
      };

    case ProjectRole.VIEWER:
      return {
        canView: true,
        canEdit: false,
        canComment: false,
        canManageMembers: false,
        canUploadFiles: false,
        canDeleteFiles: false,
        canCreateFolders: false,
        canDeleteFolders: false,
      };

    default:
      return {
        canView: false,
        canEdit: false,
        canComment: false,
        canManageMembers: false,
        canUploadFiles: false,
        canDeleteFiles: false,
        canCreateFolders: false,
        canDeleteFolders: false,
      };
  }
};

// Note-specific permission functions that align with backend logic
export const canCreateNotes = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false
): boolean => {
  const permissions = getProjectPermissions(role, isOwner);
  return permissions.canComment; // Backend uses canComment for all note operations
};

export const canEditNotes = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false
): boolean => {
  const permissions = getProjectPermissions(role, isOwner);
  return permissions.canComment; // COMMENTER can edit notes according to backend
};

export const canDeleteNotes = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false
): boolean => {
  const permissions = getProjectPermissions(role, isOwner);
  return permissions.canComment; // COMMENTER can delete notes according to backend
};

// Helper function to check if user can perform any note operations
export const canManageNotes = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false
): boolean => {
  const permissions = getProjectPermissions(role, isOwner);
  return permissions.canComment;
};

// Note deletion permission - more granular than general note permissions
export const canDeleteNote = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false,
  noteCreatedById: string,
  currentUserId: string
): boolean => {
  // Owner and Editor can delete any note
  if (isOwner) return true;

  if (role === ProjectRole.EDITOR) {
    return true; // Editor can delete all notes
  }

  if (role === ProjectRole.COMMENTER) {
    return noteCreatedById === currentUserId; // Commentor can only delete their own notes
  }

  return false; // Viewer cannot delete any notes
};

// Convert note to resource permission - only EDITOR and OWNER can convert notes to resources
export const canConvertNoteToResource = (
  role: ProjectRole | string | undefined,
  isOwner: boolean = false
): boolean => {
  // Owner can always convert notes to resources
  if (isOwner) return true;

  // Only EDITOR can convert notes to resources
  if (role === ProjectRole.EDITOR) {
    return true;
  }

  // COMMENTER and VIEWER cannot convert notes to resources
  return false;
};
