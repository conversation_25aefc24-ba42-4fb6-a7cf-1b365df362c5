## 📋 Pull Request Template

### 🎯 Description

<!-- Provide a brief description of the changes -->

### 📝 Type of Change

<!--
🤖 AUTOMATED LABELING: Checking a box below will automatically apply the corresponding GitHub label!
⚠️ Select ONLY ONE option - multiple selections will trigger a warning
✨ Labels are required for proper semantic versioning and changelog generation
-->

**Select exactly one release type:**

- [ ] 🐛 `release: patch` - Bug fix (patch version bump)
- [ ] ✨ `release: minor` - New feature (minor version bump)
- [ ] 💥 `release: major` - Breaking change (major version bump)
- [ ] 📚 `release: skip` - Documentation/CI changes (no version bump)

> **💡 How it works:** When you check a box above and save this PR, GitHub Actions will automatically apply the corresponding label. No manual labeling needed!

### 🔗 Related Issues

<!-- Link to related issues using #issue_number -->

Closes #

### 🧪 Testing

<!-- Describe the testing you have performed -->

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All tests pass

### 📚 Documentation

<!-- Check if documentation needs to be updated -->

- [ ] README updated (if needed)
- [ ] API documentation updated (if needed)
- [ ] Comments added to complex code

### 🚀 Deployment Notes

<!-- Any special deployment considerations -->

- [ ] No special deployment steps required
- [ ] Migration scripts needed
- [ ] Environment variables to be updated
- [ ] Third-party services affected

### ⚠️ Breaking Changes

<!-- If this is a breaking change, describe the impact -->

### 🏃‍♂️ Pre-merge Checklist

- [ ] Release type selected above (auto-applies label) ✨
- [ ] Code follows project conventions
- [ ] Self-review completed
- [ ] Peer review requested
- [ ] CI/CD checks passing
- [ ] Branch is up to date with develop

### 📸 Screenshots (if applicable)

<!-- Add screenshots for UI changes -->

---

**Note for Reviewers:**

- ✅ Release labels are now applied automatically based on template checkboxes
- ✅ PR validation ensures proper labeling before merge
- ✅ Verify that the changes match the selected version bump type
- ✅ Check that breaking changes are properly documented

**Automated Workflows:**

- 🤖 Auto-label PRs based on template selections
- 🔍 Validate release labels before merge
- 📝 Generate semantic releases and changelogs
