import { useMemo } from 'react';
import { useSearchParams } from 'react-router';

import { EmailInboxIcon } from 'src/assets/icons';
import { useConfirmCalendarQuery } from 'src/store/api/calendar';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

export function ConfirmMeetingView() {
  const [searchParams] = useSearchParams();
  const uid = useMemo(() => searchParams.get('uid'), [searchParams]);
  const eventId = useMemo(() => searchParams.get('eventId'), [searchParams]);
  const { isLoading, error } = useConfirmCalendarQuery({
    fUid: uid ?? '',
    eventId: eventId ?? '',
  });

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center">
        <div className="max-w-md space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">Oops!</h2>
          <div className="rounded-lg bg-red-50 p-4 text-red-700">
            <p>
              {!uid || !eventId
                ? 'Your code is missing, please check the link and try again.'
                : 'There was an error confirming your magic link, please check the link and try again.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center">
      <div className="max-w-md space-y-6">
        <div className="mx-auto h-16 w-16 rounded-full bg-green-100 p-3">
          <EmailInboxIcon className="h-full w-full text-green-600" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900">Email Verified!</h2>
        
        <div className="space-y-4 rounded-lg bg-gray-50 p-6 text-left">
          <p className="text-gray-600">
          Your email is confirmed. Aida will be scheduled for your meeting.
          </p>
        </div>
      </div>
    </div>
  );
}
