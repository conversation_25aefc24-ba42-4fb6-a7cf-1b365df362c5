import type { GenerativeModel } from 'firebase/vertexai';

import { getAuth } from 'firebase/auth';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';
import { getAI, VertexAIBackend, getGenerativeModel } from 'firebase/vertexai';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export const firebaseApp = initializeApp(CONFIG.firebase);

export const AUTH = getAuth(firebaseApp);

export const FIRESTORE = getFirestore(firebaseApp);

export const VERTEXAI = getAI(firebaseApp, {
  backend: new VertexAIBackend('us-central1'),
});

export const geminiModel: GenerativeModel = getGenerativeModel(
  VERTEXAI,
  {
    model: CONFIG.firebase.geminiModel,
    generationConfig: {
      temperature: 0.3, // Optimized for speed while maintaining quality
      topK: 40, // Increased for faster token selection with good diversity
      topP: 0.95, // Higher for faster, more natural generation
      maxOutputTokens: 65536, // Maximum for Gemini 2.5 Flash - enables comprehensive responses
    },
  },
  {
    timeout: 120 * 1000, // Optimized 2-minute timeout for chat responses
  }
);
export const firebaseAnalytics = getAnalytics(firebaseApp);
