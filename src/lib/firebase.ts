import type { GenerativeModel } from 'firebase/vertexai';

import { getAuth } from 'firebase/auth';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';
import { getAI, VertexAIBackend, getGenerativeModel } from 'firebase/vertexai';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export const firebaseApp = initializeApp(CONFIG.firebase);

export const AUTH = getAuth(firebaseApp);

export const FIRESTORE = getFirestore(firebaseApp);

export const VERTEXAI = getAI(firebaseApp, {
  backend: new VertexAIBackend('us-central1'),
});

export type GeminiModelType = 'gemini-2.5-flash' | 'gemini-2.5-pro';

// Model-specific configurations
const MODEL_CONFIGS = {
  'gemini-2.5-flash': {
    temperature: 0.3,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 65536, // Maximum for Flash
  },
  'gemini-2.5-pro': {
    temperature: 0.3,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 65536, // Maximum for Pro
  },
} as const;

// Create a model instance for a specific model type
export const createGeminiModel = (modelType: GeminiModelType): GenerativeModel => {
  const config = MODEL_CONFIGS[modelType];

  return getGenerativeModel(
    VERTEXAI,
    {
      model: modelType,
      generationConfig: config,
    },
    {
      timeout: 120 * 1000, // 2-minute timeout for chat responses
    }
  );
};

// Default model instance (for backward compatibility)
export const geminiModel: GenerativeModel = createGeminiModel(
  CONFIG.firebase.geminiModel as GeminiModelType
);
export const firebaseAnalytics = getAnalytics(firebaseApp);
