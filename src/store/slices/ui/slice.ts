import { createSlice } from '@reduxjs/toolkit';

export type UiSliceState = {
  navigationDrawerOpen: boolean;
};

export const uiInitialState: UiSliceState = {
  navigationDrawerOpen: false,
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState: uiInitialState,
  reducers: {
    openNavigationDrawer: (state) => {
      state.navigationDrawerOpen = true;
    },
    closeNavigationDrawer: (state) => {
      state.navigationDrawerOpen = false;
    },
    toggleNavigationDrawer: (state) => {
      state.navigationDrawerOpen = !state.navigationDrawerOpen;
    },
  },
});

export const { openNavigationDrawer, closeNavigationDrawer, toggleNavigationDrawer } =
  uiSlice.actions;

export default uiSlice.reducer;
