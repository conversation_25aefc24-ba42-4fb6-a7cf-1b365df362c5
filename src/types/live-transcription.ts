export interface LiveTranscriptionJwtResponse {
  jwt: string;
}

export type AudioSource = 'speaker' | 'microphone';

export interface LiveTranscriptionSegment {
  id: string;
  text: string;
  confidence: number;
  startTime: number;
  endTime: number;
  isFinal: boolean;
  speakerId?: string;
  timestamp: Date;
  audioSource?: AudioSource;
}

export interface LiveTranscriptionConfig {
  language: string;
  model?: string;
  enablePartialResults?: boolean;
  maxAlternatives?: number;
  enableSpeakerDiarization?: boolean;
  enableProfanityFilter?: boolean;
  audioQuality?: 'low' | 'medium' | 'high';
  isDualMode?: boolean;
}

export interface DualStreamState {
  isCapturingScreen: boolean;
  speakerConnected: boolean;
  microphoneConnected: boolean;
  speakerConnectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  microphoneConnectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  speakerError: string | null;
  microphoneError: string | null;
}

export interface LiveTranscriptionState {
  isConnected: boolean;
  isRecording: boolean;
  isTranscribing: boolean;
  segments: LiveTranscriptionSegment[];
  error: string | null;
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  audioLevel?: number;
  latency?: number;
  dualStream?: DualStreamState;
  isDualMode?: boolean;
}

export enum LiveTranscriptionSocketState {
  Disconnected = 'disconnected',
  Connecting = 'connecting',
  Connected = 'connected',
  Error = 'error',
}

export interface LiveTranscriptionResult {
  audioFile: Blob;
  transcriptData: {
    sentence: string;
    startTime: number;
    endTime: number;
    speakerId: string;
    content: string;
  }[];
  duration: number;
  startTime: Date;
  endTime: Date;
}

export interface UseLiveTranscriptionReturn {
  state: LiveTranscriptionState;
  startTranscription: (config: LiveTranscriptionConfig) => Promise<void>;
  stopTranscription: () => Promise<LiveTranscriptionResult>;
  clearTranscription: () => void;
  exportTranscription: () => string;
  bootstrap: () => Promise<void>;
  audioDevices: MediaDeviceInfo[];
  selectedAudioDeviceId?: string;
  switchAudioDevice: (deviceId: string) => Promise<void>;
  getAudioLevel: () => number;
  getSegmentsBySource: (source?: AudioSource) => LiveTranscriptionSegment[];
  exportTranscriptionBySource: (source?: AudioSource) => string;
}
