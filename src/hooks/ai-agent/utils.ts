import type { UsageMetadata } from 'firebase/ai';

import type useAnalytics from '../analytics';

// Cost tracking constants for Gemini 2.5 Pro
const GEMINI_25_PRO_PRICING = {
  inputTokensPerMillion: 1.25, // $1.25 per 1M input tokens
  outputTokensPerMillion: 10.0, // $10.00 per 1M output tokens
} as const;

// Helper function to calculate costs
const calculateCost = (usage: UsageMetadata) => {
  const inputCost =
    (usage.promptTokenCount / 1_000_000) * GEMINI_25_PRO_PRICING.inputTokensPerMillion;
  const outputCost =
    (usage.candidatesTokenCount / 1_000_000) * GEMINI_25_PRO_PRICING.outputTokensPerMillion;
  const totalCost = inputCost + outputCost;

  return {
    inputCost: Number(inputCost.toFixed(6)),
    outputCost: Number(outputCost.toFixed(6)),
    totalCost: Number(totalCost.toFixed(6)),
    inputTokens: usage.promptTokenCount,
    outputTokens: usage.candidatesTokenCount,
    totalTokens: usage.totalTokenCount,
  };
};

// Helper function to log detailed usage metrics
export const logUsageMetrics = (
  trackEvent: ReturnType<typeof useAnalytics>['trackEvent'],
  usage: UsageMetadata,
  context: {
    action: string;
    messageLength?: number;
    fileCount?: number;
    conversationLength?: number;
    userId?: string;
  }
) => {
  const costData = calculateCost(usage);

  // Track to PostHog for detailed analytics with dashboard-friendly structure
  trackEvent({
    eventCategory: 'AI Usage',
    eventAction: `Gemini ${context.action}`,
    properties: {
      // Model identification
      ai_model: 'gemini-2.5-pro',
      ai_provider: 'google',
      ai_action_type: context.action.toLowerCase().replace(' ', '_'),

      // Token metrics (for usage dashboards)
      tokens_input: costData.inputTokens,
      tokens_output: costData.outputTokens,
      tokens_total: costData.totalTokens,

      // Cost metrics (for financial dashboards)
      cost_input_usd: costData.inputCost,
      cost_output_usd: costData.outputCost,
      cost_total_usd: costData.totalCost,
      cost_per_token: costData.totalTokens > 0 ? costData.totalCost / costData.totalTokens : 0,

      // Context metrics (for usage analysis)
      message_length: context.messageLength || 0,
      file_count: context.fileCount || 0,
      conversation_length: context.conversationLength || 0,

      // Session info
      user_id: context.userId,
      session_date: new Date().toISOString().split('T')[0], // YYYY-MM-DD for daily aggregation
      session_hour: new Date().getHours(), // 0-23 for hourly analysis

      // Cost categorization (for alerts and analysis)
      cost_tier:
        costData.totalCost >= 2.0
          ? 'high'
          : costData.totalCost >= 0.5
            ? 'medium'
            : costData.totalCost >= 0.1
              ? 'low'
              : 'minimal',

      // Efficiency metrics
      tokens_per_dollar: costData.totalCost > 0 ? costData.totalTokens / costData.totalCost : 0,
      cost_per_message_char:
        context.messageLength && context.messageLength > 0
          ? costData.totalCost / context.messageLength
          : 0,
    },
  });

  // Log to console for development monitoring
  if (import.meta.env.MODE === 'development') {
    console.group('🤖 Gemini 2.5 Pro Usage');
    console.log('Action:', context.action);
    console.log(
      'Tokens:',
      `${costData.inputTokens} input + ${costData.outputTokens} output = ${costData.totalTokens} total`
    );
    console.log(
      'Cost:',
      `$${costData.inputCost} input + $${costData.outputCost} output = $${costData.totalCost} total`
    );
    console.log('Context:', context);
    console.groupEnd();
  }
};
