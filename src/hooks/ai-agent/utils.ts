import type { UsageMetadata } from 'firebase/ai';
import type { GeminiModelType } from 'src/lib/firebase';

import type useAnalytics from '../analytics';

// Cost tracking constants for Gemini models (USD per 1M tokens)
// Based on latest Google Cloud pricing as of 2025
const MODEL_PRICING = {
  'gemini-2.5-flash': {
    inputTokensPerMillion: 0.075, // $0.075 per 1M input tokens (significantly cheaper)
    outputTokensPerMillion: 0.3, // $0.30 per 1M output tokens (4x cheaper than Pro)
    displayName: 'Gemini 2.5 Flash',
  },
  'gemini-2.5-pro': {
    inputTokensPerMillion: 1.25, // $1.25 per 1M input tokens
    outputTokensPerMillion: 10.0, // $10.00 per 1M output tokens
    displayName: 'Gemini 2.5 Pro',
  },
} as const;

// Helper function to calculate costs for specific model
const calculateCost = (usage: UsageMetadata, modelType: GeminiModelType) => {
  const pricing = MODEL_PRICING[modelType];

  const inputCost = (usage.promptTokenCount / 1_000_000) * pricing.inputTokensPerMillion;
  const outputCost = (usage.candidatesTokenCount / 1_000_000) * pricing.outputTokensPerMillion;
  const totalCost = inputCost + outputCost;

  return {
    inputCost: Number(inputCost.toFixed(6)),
    outputCost: Number(outputCost.toFixed(6)),
    totalCost: Number(totalCost.toFixed(6)),
    inputTokens: usage.promptTokenCount,
    outputTokens: usage.candidatesTokenCount,
    totalTokens: usage.totalTokenCount,
    modelType,
    modelDisplayName: pricing.displayName,
    // Cost efficiency metrics
    costPerInputToken: pricing.inputTokensPerMillion / 1_000_000,
    costPerOutputToken: pricing.outputTokensPerMillion / 1_000_000,
  };
};

// Helper function to log detailed usage metrics with model-specific cost tracking
export const logUsageMetrics = (
  trackEvent: ReturnType<typeof useAnalytics>['trackEvent'],
  usage: UsageMetadata,
  context: {
    action: string;
    modelType: GeminiModelType;
    messageLength?: number;
    fileCount?: number;
    conversationLength?: number;
    userId?: string;
  }
) => {
  const costData = calculateCost(usage, context.modelType);

  // Track to PostHog for detailed analytics with dashboard-friendly structure
  trackEvent({
    eventCategory: 'AI Usage',
    eventAction: `${costData.modelDisplayName} ${context.action}`,
    properties: {
      // Model identification
      ai_model: costData.modelType,
      ai_model_display: costData.modelDisplayName,
      ai_provider: 'google',
      ai_action_type: context.action.toLowerCase().replace(' ', '_'),

      // Token metrics (for usage dashboards)
      tokens_input: costData.inputTokens,
      tokens_output: costData.outputTokens,
      tokens_total: costData.totalTokens,

      // Cost metrics (for financial dashboards)
      cost_input_usd: costData.inputCost,
      cost_output_usd: costData.outputCost,
      cost_total_usd: costData.totalCost,
      cost_per_token: costData.totalTokens > 0 ? costData.totalCost / costData.totalTokens : 0,

      // Context metrics (for usage analysis)
      message_length: context.messageLength || 0,
      file_count: context.fileCount || 0,
      conversation_length: context.conversationLength || 0,

      // Session info
      user_id: context.userId,
      session_date: new Date().toISOString().split('T')[0], // YYYY-MM-DD for daily aggregation
      session_hour: new Date().getHours(), // 0-23 for hourly analysis

      // Cost categorization (for alerts and analysis)
      cost_tier:
        costData.totalCost >= 2.0
          ? 'high'
          : costData.totalCost >= 0.5
            ? 'medium'
            : costData.totalCost >= 0.1
              ? 'low'
              : 'minimal',

      // Model-specific cost efficiency metrics
      cost_per_input_token: costData.costPerInputToken,
      cost_per_output_token: costData.costPerOutputToken,
      tokens_per_dollar: costData.totalCost > 0 ? costData.totalTokens / costData.totalCost : 0,
      cost_per_message_char:
        context.messageLength && context.messageLength > 0
          ? costData.totalCost / context.messageLength
          : 0,

      // Model comparison metrics (for cost optimization insights)
      model_cost_efficiency: costData.modelType === 'gemini-2.5-flash' ? 'high' : 'standard',
      estimated_flash_cost:
        costData.modelType === 'gemini-2.5-pro'
          ? Number(
              (
                (costData.inputTokens / 1_000_000) *
                  MODEL_PRICING['gemini-2.5-flash'].inputTokensPerMillion +
                (costData.outputTokens / 1_000_000) *
                  MODEL_PRICING['gemini-2.5-flash'].outputTokensPerMillion
              ).toFixed(6)
            )
          : costData.totalCost,
      estimated_pro_cost:
        costData.modelType === 'gemini-2.5-flash'
          ? Number(
              (
                (costData.inputTokens / 1_000_000) *
                  MODEL_PRICING['gemini-2.5-pro'].inputTokensPerMillion +
                (costData.outputTokens / 1_000_000) *
                  MODEL_PRICING['gemini-2.5-pro'].outputTokensPerMillion
              ).toFixed(6)
            )
          : costData.totalCost,
    },
  });

  // Log to console for development monitoring with model comparison
  if (import.meta.env.MODE === 'development') {
    const isFlash = costData.modelType === 'gemini-2.5-flash';
    const icon = isFlash ? '⚡' : '🧠';

    console.group(`${icon} ${costData.modelDisplayName} Usage`);
    console.log('Action:', context.action);
    console.log(
      'Tokens:',
      `${costData.inputTokens} input + ${costData.outputTokens} output = ${costData.totalTokens} total`
    );
    console.log(
      'Cost:',
      `$${costData.inputCost} input + $${costData.outputCost} output = $${costData.totalCost} total`
    );

    // Show cost comparison for optimization insights
    if (isFlash) {
      const proCost = Number(
        (
          (costData.inputTokens / 1_000_000) *
            MODEL_PRICING['gemini-2.5-pro'].inputTokensPerMillion +
          (costData.outputTokens / 1_000_000) *
            MODEL_PRICING['gemini-2.5-pro'].outputTokensPerMillion
        ).toFixed(6)
      );
      const savings = Number((proCost - costData.totalCost).toFixed(6));
      const savingsPercent = proCost > 0 ? Number(((savings / proCost) * 100).toFixed(1)) : 0;
      console.log(`💰 Savings vs Pro: $${savings} (${savingsPercent}%)`);
    } else {
      const flashCost = Number(
        (
          (costData.inputTokens / 1_000_000) *
            MODEL_PRICING['gemini-2.5-flash'].inputTokensPerMillion +
          (costData.outputTokens / 1_000_000) *
            MODEL_PRICING['gemini-2.5-flash'].outputTokensPerMillion
        ).toFixed(6)
      );
      const premium = Number((costData.totalCost - flashCost).toFixed(6));
      const premiumPercent = flashCost > 0 ? Number(((premium / flashCost) * 100).toFixed(1)) : 0;
      console.log(`💎 Premium vs Flash: $${premium} (${premiumPercent}% more)`);
    }

    console.log('Context:', context);
    console.groupEnd();
  }
};
