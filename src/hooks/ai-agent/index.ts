import type { ProjectDetails } from 'src/types/project';
import type { Resource, IChatMessage, Transcription } from 'src/types';
import type { Part, ChatSession, UsageMetadata } from 'firebase/vertexai';

import { toast } from 'sonner';
import * as Sentry from '@sentry/react';
import { useState, useCallback } from 'react';
import { uuidv4 } from 'minimal-shared/utils';

import { fSub, fSecsToTime } from 'src/utils/format-time';

import { AUTH, geminiModel } from 'src/lib/firebase';

import { fileFormat } from 'src/components/file-thumbnail';

import useAnalytics from '../analytics';
import { logUsageMetrics } from './utils';

const SYSTEM_INSTRUCTIONS: Part[] = [
  {
    text: `# AI Assistant Instructions (Gemini 2.5 Pro)

## Role
You are a helpful AI assistant with hybrid reasoning capabilities, focused on analyzing files and their content. Provide comprehensive, well-reasoned responses while maintaining accuracy and clarity.

## Enhanced Capabilities
With Gemini 2.5 Pro, you have:
- **Hybrid Reasoning**: Combine speed with thoughtful analysis
- **Extended Responses**: Up to 65,536 tokens for detailed explanations
- **Advanced Context Processing**: 1M token context window for comprehensive understanding
- **Enhanced Multimodal Analysis**: Improved understanding of text, images, PDFs, and multimedia content

## Context Handling
- Files: "name" (ID: id)
- Folders: "name" (ID: id)  
- Projects: "name" (ID: id)
- Transcriptions: <speaker>: <startTime>-<endTime>: <content>

## Supported File Types
**Direct AI Analysis:** Text (.txt, .md), PDF (.pdf), Images (.jpg, .jpeg, .png, .webp, .gif, .bmp, .svg)

**Transcription-Based Analysis:** Audio (.wav, .aif, .mp3, .aac, .m4a, .ogg, .flac, .wma, .m4r, .amr), Video (.m4v, .avi, .mpg, .mp4, .webm, .mov, .wmv, .flv, .mkv, .vob, .ogg) - analyzed via transcripts with speaker names and timestamps

**Metadata Reference:** ZIP/Archive files (.zip, .rar, .iso), Design files (.psd, .ai, .esp) - file names and metadata can be referenced

**Note:** Office documents (.doc, .docx, .ppt, .pptx, .xls, .xlsx) can be uploaded and stored, but AI analysis support is coming soon!

## Output Formatting Capabilities
1. **Rich Formatting**: Use markdown syntax for comprehensive, well-structured content
2. **Detailed Analysis**: Provide thorough explanations taking advantage of extended response capacity
3. **Creative Presentations**: Use tables, diagrams, flowcharts, lists, ASCII art, and structured layouts
4. **Code Excellence**: Support syntax highlighting, detailed code explanations, and technical documentation
5. **Comprehensive Responses**: Utilize full response capacity for complete, nuanced answers
6. **Professional Documentation**: Create detailed reports, summaries, and analytical content

## Guidelines
1. **Comprehensive Analysis**: Use full reasoning capabilities for thorough file analysis
2. **Detailed Responses**: Take advantage of extended token limit for complete explanations
3. **Context Mastery**: Efficiently process and reference large amounts of contextual information
4. **Creative Formatting**: Use the most appropriate format for clear, engaging information presentation
5. **Professional Quality**: Deliver detailed, well-reasoned responses worthy of the enhanced model capabilities
6. **Continuous Context**: Maintain awareness of entire conversation history and file context
7. **Hybrid Reasoning**: Balance speed with thoughtful analysis for optimal results
8. **Extended Explanations**: When helpful, provide comprehensive explanations rather than brief responses
9. **Never mention file IDs**: Always reference files by name only
10. **Intelligent Continuation**: If response approaches limits, use "...[continued]" and maintain context flow`,
  },
];

// File type configuration for supported formats
const FILE_TYPE_CONFIG = {
  // Text files - Supported by Firebase AI Logic for direct analysis
  txt: {
    mimeType: 'text/plain',
    processingType: 'text' as const,
    maxSizeMB: 10,
  },
  // PDF files - Supported by Firebase AI Logic for direct analysis
  pdf: {
    mimeType: 'application/pdf',
    processingType: 'binary' as const,
    maxSizeMB: 18,
  },
  // Image files - Supported by Firebase AI Logic for direct analysis
  // Note: fileFormat() returns 'image' for all image types, so we use that as key
  image: {
    mimeType: 'image/*', // MIME type will be determined from file extension
    processingType: 'binary' as const,
    maxSizeMB: 7,
  },
  // Audio files - Supported via transcriptions (covers: wav, aif, mp3, aac, m4a, ogg, flac, wma, m4r, amr)
  audio: {
    mimeType: 'audio/*', // MIME type not used since we process transcriptions, not file content
    processingType: 'transcription' as const,
    maxSizeMB: 0, // Not applicable for transcription-based processing
  },
  // Video files - Supported via transcriptions (covers: m4v, avi, mpg, mp4, webm, mov, wmv, flv, mkv, vob, ogg)
  video: {
    mimeType: 'video/*', // MIME type not used since we process transcriptions, not file content
    processingType: 'transcription' as const,
    maxSizeMB: 0, // Not applicable for transcription-based processing
  },
  // Archive files - Can be stored and analyzed if they contain extractable content
  zip: {
    mimeType: 'application/zip', // MIME type not used for content analysis
    processingType: 'metadata' as const,
    maxSizeMB: 0, // File is stored but content not directly analyzed
  },
  // Design files - Can be stored but content not directly analyzed by AI
  photoshop: {
    mimeType: 'application/photoshop', // MIME type not used for content analysis
    processingType: 'metadata' as const,
    maxSizeMB: 0, // File is stored but content not directly analyzed
  },
  illustrator: {
    mimeType: 'application/illustrator', // MIME type not used for content analysis
    processingType: 'metadata' as const,
    maxSizeMB: 0, // File is stored but content not directly analyzed
  },
  // Note: Office documents (word, excel, powerpoint) are not yet supported for AI analysis
  // Support for Office documents is coming soon!
} as const;

type SupportedFileFormat = keyof typeof FILE_TYPE_CONFIG;

// Constants for chunking large transcriptions
const MAX_CHUNK_SIZE = 1000000;

const useAiAgent = () => {
  const { trackEvent } = useAnalytics();

  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<IChatMessage[]>([]);
  const [isReplying, setIsReplying] = useState(false);
  const [isStartingConversation, setIsStartingConversation] = useState(false);
  const [pendingInstructions, setPendingInstructions] = useState<Part[]>([]);
  const [activeResources, setActiveResources] = useState<Resource[]>([]);

  // Performance optimizations: caching and processing status
  const [processedFiles, setProcessedFiles] = useState<Map<string, Part[]>>(new Map());
  const [processingFiles, setProcessingFiles] = useState<Set<string>>(new Set());

  // Abort controller for stopping conversations
  const [abortController, setAbortController] = useState<AbortController | null>(null);

  // Helper function to format transcription text
  const formatTranscriptionText = (transcription: Transcription) =>
    `${transcription.nameFromRevAi}: ${fSecsToTime(transcription.startTime)}-${fSecsToTime(transcription.endTime)}: ${transcription.content}`;

  // Convert transcriptions to text file format with chunking support
  const convertTranscriptionsToTextFile = (transcriptions: Transcription[]): Part[] => {
    if (!transcriptions?.length) return [];

    const textContent = transcriptions.map(formatTranscriptionText).join('\n\n');
    const encodedContent = btoa(unescape(encodeURIComponent(textContent)));

    if (encodedContent.length > MAX_CHUNK_SIZE) {
      const chunks = [];
      for (let i = 0; i < encodedContent.length; i += MAX_CHUNK_SIZE) {
        chunks.push(encodedContent.slice(i, i + MAX_CHUNK_SIZE));
      }

      return [
        { text: 'Transcription content (split into multiple parts):' },
        ...chunks.map((chunk) => ({
          inlineData: { data: chunk, mimeType: 'text/plain' },
        })),
      ];
    }

    return [
      { text: 'Transcription content:' },
      { inlineData: { data: encodedContent, mimeType: 'text/plain' } },
    ];
  };

  // Check if file format is supported
  const isSupportedFileFormat = (format: string): format is SupportedFileFormat =>
    format in FILE_TYPE_CONFIG;

  // Check file size and determine if it can be processed
  const checkFileSize = async (
    url: string,
    format: SupportedFileFormat
  ): Promise<{ sizeMB: number; canProcess: boolean }> => {
    try {
      const config = FILE_TYPE_CONFIG[format];

      // For transcription and metadata processing types, size doesn't matter
      if (config.processingType === 'transcription' || config.processingType === 'metadata') {
        return { sizeMB: 0, canProcess: true };
      }

      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('Content-Length');
      const sizeMB = contentLength ? parseInt(contentLength, 10) / (1024 * 1024) : 0;

      return {
        sizeMB,
        canProcess: sizeMB <= config.maxSizeMB && sizeMB > 0,
      };
    } catch {
      return { sizeMB: 0, canProcess: false };
    }
  };

  // Fetch and encode file content
  const fetchFileContent = async (url: string, format: SupportedFileFormat): Promise<string> => {
    const config = FILE_TYPE_CONFIG[format];

    // For transcription and metadata processing, we don't fetch file content
    if (config.processingType === 'transcription' || config.processingType === 'metadata') {
      return ''; // Return empty string as content is not processed
    }

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch ${format.toUpperCase()} file: ${response.statusText}`);
    }

    if (config.processingType === 'text') {
      const textContent = await response.text();
      return btoa(unescape(encodeURIComponent(textContent)));
    }

    const arrayBuffer = await response.arrayBuffer();
    const sizeMB = arrayBuffer.byteLength / (1024 * 1024);

    if (sizeMB > config.maxSizeMB) {
      throw new Error(
        `${format.toUpperCase()} file too large: ${sizeMB.toFixed(1)}MB (max ${config.maxSizeMB}MB)`
      );
    }

    const bytes = new Uint8Array(arrayBuffer);
    let binary = '';
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i]);
    }

    return btoa(binary);
  };

  // Get proper MIME type for images
  const getImageMimeType = (fileName: string): string => {
    const extension = fileName.toLowerCase().split('.').pop() || '';
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      bmp: 'image/bmp',
      tiff: 'image/tiff',
      tif: 'image/tiff',
    };
    return mimeTypes[extension] || 'image/jpeg';
  };

  // Helper function to handle errors consistently
  const handleFileError = (resource: Resource, error: unknown, cacheKey: string): Part[] => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorParts = [
      { text: `Content for file "${resource.name}" (ID: ${resource.id}):` },
      { text: `[Error loading content: ${errorMessage}]` },
    ];
    setProcessedFiles((prev) => new Map(prev.set(cacheKey, errorParts)));
    return errorParts;
  };

  // Format file data with caching and error handling
  const formatFileData = async (resource: Resource): Promise<Part[]> => {
    const detectedFormat = fileFormat(resource.fileName || resource.name || '');

    if (!isSupportedFileFormat(detectedFormat)) {
      const parts: Part[] = [{ text: `File "${resource.name}" (ID: ${resource.id}):` }];

      if (['word', 'powerpoint', 'excel'].includes(detectedFormat)) {
        parts.push({
          text: `[📋 ${detectedFormat.toUpperCase()} files are not yet supported for AI analysis, but support is coming soon! For now, you can convert to PDF and upload the PDF version for AI analysis.]`,
        });
      } else {
        parts.push({
          text: `[ℹ️ This file type is not currently supported for AI analysis. Currently supported: Text files, PDF, Images (direct analysis), Audio/Video (via transcripts), ZIP/Archive files, and Design files (Photoshop, Illustrator) for metadata reference.]`,
        });
      }

      return parts;
    }

    // Handle special processing types
    const fileConfig = FILE_TYPE_CONFIG[detectedFormat];

    // Audio/Video files processed via transcriptions
    if (fileConfig.processingType === 'transcription') {
      const parts: Part[] = [{ text: `File "${resource.name}" (ID: ${resource.id}):` }];

      if (resource.transcription && resource.transcription.length > 0) {
        parts.push({
          text: `[🎬 ${detectedFormat.toUpperCase()} file content processed via transcription]`,
        });
      } else {
        parts.push({
          text: `[⏳ ${detectedFormat.toUpperCase()} file is ready for AI analysis once transcription is complete]`,
        });
      }

      return parts;
    }

    // Files that are stored but not directly analyzed (ZIP, design files, etc.)
    if (fileConfig.processingType === 'metadata') {
      const parts: Part[] = [{ text: `File "${resource.name}" (ID: ${resource.id}):` }];

      parts.push({
        text: `[📁 ${detectedFormat.toUpperCase()} file is stored and available. File metadata and name can be referenced, but content analysis is not currently supported for this file type.]`,
      });

      return parts;
    }

    const cacheKey = `${resource.id}-${resource.name}-${detectedFormat}`;

    // Return cached result if available
    if (processedFiles.has(cacheKey)) {
      return processedFiles.get(cacheKey)!;
    }

    // Return processing status if currently processing
    if (processingFiles.has(cacheKey)) {
      return [
        { text: `Content for file "${resource.name}" (ID: ${resource.id}):` },
        { text: `[File "${resource.name}" is being processed...]` },
      ];
    }

    const config = FILE_TYPE_CONFIG[detectedFormat];
    const parts: Part[] = [{ text: `Content for file "${resource.name}" (ID: ${resource.id}):` }];

    if (!resource.url) {
      const errorParts = [
        ...parts,
        { text: `[Error: No URL available for file "${resource.name}"]` },
      ];
      setProcessedFiles((prev) => new Map(prev.set(cacheKey, errorParts)));
      return errorParts;
    }

    try {
      setProcessingFiles((prev) => new Set(prev.add(cacheKey)));

      const { sizeMB, canProcess } = await checkFileSize(resource.url, detectedFormat);

      if (!canProcess) {
        const sizeParts = [
          ...parts,
          {
            text: `[File "${resource.name}" (${sizeMB.toFixed(1)}MB) exceeds size limit of ${config.maxSizeMB}MB and was not processed]`,
          },
        ];
        setProcessedFiles((prev) => new Map(prev.set(cacheKey, sizeParts)));
        return sizeParts;
      }

      const base64Content = await fetchFileContent(resource.url, detectedFormat);

      if (config.processingType === 'text') {
        const contentSizeMB = (base64Content.length * 3) / 4 / (1024 * 1024);

        if (contentSizeMB > config.maxSizeMB) {
          const maxChars = Math.floor((config.maxSizeMB * 1024 * 1024) / 3);
          const originalContent = decodeURIComponent(escape(atob(base64Content)));
          const truncatedContent = originalContent.substring(0, maxChars);
          const truncatedEncoded = btoa(
            unescape(
              encodeURIComponent(truncatedContent + '\n\n[File truncated due to size limits]')
            )
          );

          parts.push({
            inlineData: { data: truncatedEncoded, mimeType: config.mimeType },
          });
        } else {
          parts.push({
            inlineData: { data: base64Content, mimeType: config.mimeType },
          });
        }
      } else {
        // For images, get the proper MIME type based on file extension
        const mimeType =
          detectedFormat === 'image'
            ? getImageMimeType(resource.fileName || resource.name || '')
            : config.mimeType;

        parts.push({
          inlineData: { data: base64Content, mimeType },
        });
      }

      setProcessedFiles((prev) => new Map(prev.set(cacheKey, parts)));
      return parts;
    } catch (error) {
      return handleFileError(resource, error, cacheKey);
    } finally {
      setProcessingFiles((prev) => {
        const newSet = new Set(prev);
        newSet.delete(cacheKey);
        return newSet;
      });
    }
  };

  // Format instruction for a resource
  const formatInstructionForResource = async (resource: Resource): Promise<Part[]> => {
    const parts: Part[] = [
      { text: `File "${resource.name}" (ID: ${resource.id})` },
      ...(resource.projectId ? [{ text: `Project (ID: ${resource.projectId})` }] : []),
      ...(resource.folderId ? [{ text: `Folder (ID: ${resource.folderId})` }] : []),
      ...(resource.transcription ? convertTranscriptionsToTextFile(resource.transcription) : []),
    ];

    const fileDataParts = await formatFileData(resource);
    parts.push(...fileDataParts);

    return parts;
  };

  // Append message to conversation
  const appendMessage = (message: string, sender: 'user' | 'bot', existingMessageId?: string) => {
    const userId = AUTH.currentUser?.uid ?? 'user';
    const messageId = existingMessageId ?? uuidv4();

    if (existingMessageId) {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === existingMessageId ? { ...msg, body: msg.body + message } : msg
        )
      );
    } else {
      const messageData: IChatMessage = {
        id: messageId,
        body: message,
        createdAt: fSub({ minutes: 1 }),
        senderId: sender === 'user' ? userId : 'bot',
      };
      setMessages((prev) => [...prev, messageData]);
    }

    return messageId;
  };

  // Start a new conversation
  const startConversation = async (instructions: Part[] = []) => {
    if (session) {
      return undefined;
    }

    try {
      setIsStartingConversation(true);

      const newSession = geminiModel.startChat({
        history: [
          {
            role: 'user',
            parts: SYSTEM_INSTRUCTIONS,
          },
        ],
      });
      setSession(newSession);

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Started conversation',
        properties: { instructionCount: instructions.length },
      });

      if (instructions.length > 0) {
        const contextMessage = [
          ...instructions,
          {
            text: 'Initial context processed. Ready to answer questions about the provided files.',
          },
        ];
        const response = await newSession.sendMessage(contextMessage);

        // Log cost metrics for context initialization
        if (response.response.usageMetadata) {
          logUsageMetrics(trackEvent, response.response.usageMetadata, {
            action: 'Context Initialization',
            fileCount: instructions.length,
            userId: AUTH.currentUser?.uid,
          });
        }
      }

      appendMessage('How can I assist you today?', 'bot');
      return newSession;
    } catch (error) {
      toast.error('Failed to start conversation', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      Sentry.captureException(error);
      return undefined;
    } finally {
      setIsStartingConversation(false);
    }
  };

  // Simple streaming update logic - let Markdown handle all formatting
  const createUpdateMessage = (botMessageId: string) => {
    let lastUpdateTime = Date.now();

    return (content: string, forceUpdate = false) => {
      const currentTime = Date.now();
      const timeSinceLastUpdate = currentTime - lastUpdateTime;

      // Simple update strategy: regular time-based updates
      const shouldUpdate =
        forceUpdate ||
        timeSinceLastUpdate > 500 || // Update every 500ms for smooth streaming
        content.length === 0; // Always update on first chunk

      if (shouldUpdate) {
        lastUpdateTime = currentTime;
        setMessages((prev) =>
          prev.map((msg) => (msg.id === botMessageId ? { ...msg, body: content } : msg))
        );
      }
    };
  };

  // Stop conversation and abort current processing
  const stopConversation = useCallback(() => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
    setIsReplying(false);

    // Remove any empty bot messages that might have been created for streaming
    setMessages((prev) =>
      prev.filter((msg) => !(msg.senderId === 'bot' && (!msg.body || msg.body === '')))
    );

    trackEvent({
      eventCategory: 'Aida Chat',
      eventAction: 'Stopped conversation',
    });
  }, [abortController, trackEvent]);

  // Send message with streaming response
  const sendMessage = useCallback(
    async (message: string) => {
      if (!session) {
        toast.error('Please start a conversation first');
        return;
      }

      // Create new abort controller for this request
      const controller = new AbortController();
      setAbortController(controller);

      try {
        appendMessage(message, 'user');
        setIsReplying(true);

        const messageParts: Part[] = [];
        let contextIncluded = false;

        // Add pending instructions if any
        if (pendingInstructions.length > 0) {
          contextIncluded = true;
          messageParts.push(...pendingInstructions);
          messageParts.push({
            text: '--- Context updates processed. Now responding to user question ---',
          });
          setPendingInstructions([]);
        }

        // Add the user message
        messageParts.push({ text: message });

        // Send streaming message
        const result = await session.sendMessageStream(messageParts);
        const botMessageId = appendMessage('', 'bot');
        const updateMessage = createUpdateMessage(botMessageId);

        let usage: UsageMetadata | undefined;
        let accumulatedText = '';

        // Process streaming response with abort signal check
        for await (const chunk of result.stream) {
          // Check if the request was aborted
          if (controller.signal.aborted) {
            break;
          }

          const chunkText = chunk.text();
          usage = chunk.usageMetadata;
          accumulatedText += chunkText;
          updateMessage(accumulatedText);
        }

        // Only show final update if not aborted
        if (!controller.signal.aborted) {
          updateMessage(accumulatedText, true);

          // Log detailed cost metrics if usage data is available
          if (usage) {
            logUsageMetrics(trackEvent, usage, {
              action: 'Chat Message',
              messageLength: message.length,
              conversationLength: messages.length,
              fileCount: activeResources.length,
              userId: AUTH.currentUser?.uid,
            });
          }

          trackEvent({
            eventCategory: 'Aida Chat',
            eventAction: 'Sent message',
            properties: {
              message,
              usage,
              contextIncluded,
              activeResourceCount: activeResources.length,
              responseLength: accumulatedText.length,
            },
          });
        }
      } catch (error) {
        // Don't show error if the request was aborted
        if (controller.signal.aborted) {
          return;
        }

        toast.error('Failed to send message', {
          description: 'Something went wrong. Please try again later.',
        });

        Sentry.captureException(error, {
          extra: {
            message,
            activeResourceCount: activeResources.length,
            pendingInstructionsCount: pendingInstructions.length,
            conversationState: {
              hasSession: !!session,
              messageCount: messages.length,
              isReplying,
              isStartingConversation,
            },
          },
          tags: { error_type: 'ai_message_error' },
        });
      } finally {
        // Only reset states if not aborted (stopConversation handles this)
        if (!controller.signal.aborted) {
          setIsReplying(false);
          setAbortController(null);
        }
      }
    },
    [
      session,
      pendingInstructions,
      activeResources,
      messages.length,
      isReplying,
      isStartingConversation,
      trackEvent,
    ]
  );

  // Update context with new resources
  const updateContextWithNewResources = useCallback(
    async (newResources: Resource[]) => {
      if (!newResources.length) return;

      const uniqueNewResources = newResources.filter(
        (newRes) => !activeResources.some((existingRes) => existingRes.id === newRes.id)
      );

      if (!uniqueNewResources.length) return;

      try {
        setActiveResources((prev) => [...prev, ...uniqueNewResources]);

        const newInstructions: Part[] = [
          {
            text: `📁 ADDING ${uniqueNewResources.length} new files to context:\n${uniqueNewResources
              .map((r) => `- "${r.name}" (ID: ${r.id})`)
              .join('\n')}`,
          },
        ];

        const instructionPromises = uniqueNewResources.map(formatInstructionForResource);
        const resourceInstructions = await Promise.all(instructionPromises);
        resourceInstructions.forEach((instruction) => newInstructions.push(...instruction));

        newInstructions.push({
          text: 'These are additional context files. Process them and use them for all subsequent questions. No need to acknowledge or repeat this context in responses.',
        });

        setPendingInstructions((prev) => [...prev, ...newInstructions]);

        trackEvent({
          eventCategory: 'Aida Chat',
          eventAction: 'Updated context',
          properties: { newResourceCount: uniqueNewResources.length },
        });
      } catch (error) {
        toast.error('Failed to update context', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
        Sentry.captureException(error);
      }
    },
    [activeResources, trackEvent]
  );

  // Remove resources from context
  const removeResourcesFromContext = useCallback(
    async (resourcesToRemove: Resource[]) => {
      if (!resourcesToRemove.length) return;

      try {
        setActiveResources((prev) =>
          prev.filter((res) => !resourcesToRemove.some((r) => r.id === res.id))
        );

        const remainingResources = activeResources.filter(
          (res) => !resourcesToRemove.some((r) => r.id === res.id)
        );

        const removeInstructions: Part[] = [
          {
            text: `🗑️ REMOVING ${resourcesToRemove.length} files from context:\n${resourcesToRemove
              .map((r) => `- "${r.name}" (ID: ${r.id})`)
              .join('\n')}`,
          },
          {
            text: `IMPORTANT: The above files have been removed from your context. Do NOT reference, mention, or use any information from these files in future responses. Forget their contents completely. Only use files that are currently in your active context.`,
          },
          {
            text: `Current active files after removal: ${
              remainingResources.map((r) => `"${r.name}"`).join(', ') || 'None'
            }`,
          },
        ];

        setPendingInstructions((prev) => [...prev, ...removeInstructions]);

        trackEvent({
          eventCategory: 'Aida Chat',
          eventAction: 'Removed from context',
          properties: { removedResourceCount: resourcesToRemove.length },
        });
      } catch (error) {
        toast.error('Failed to remove files from context', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
        Sentry.captureException(error);
      }
    },
    [activeResources, trackEvent]
  );

  // Start conversation with multiple resources
  const startConversationWithResources = useCallback(async (resources: Resource[]) => {
    if (resources.length === 0) {
      await startConversation([]);
      return;
    }

    const instructions: Part[] = [
      {
        text: `Analyzing ${resources.length} files:\n${resources.map((r) => `- "${r.name}" (ID: ${r.id})`).join('\n')}`,
      },
    ];

    const instructionPromises = resources.map(formatInstructionForResource);
    const resourceInstructions = await Promise.all(instructionPromises);
    resourceInstructions.forEach((instruction) => instructions.push(...instruction));

    setActiveResources(resources);
    await startConversation(instructions);
  }, []);

  // Start conversation for a single resource
  const startConversationForResource = async (resource: Resource) => {
    if (!resource) return;

    const instructions: Part[] = [
      { text: `Analyzing file "${resource.name}" (ID: ${resource.id})` },
    ];

    const resourceInstructions = await formatInstructionForResource(resource);
    instructions.push(...resourceInstructions);

    setActiveResources([resource]);
    await startConversation(instructions);
  };

  // Start conversation for a project
  const startConversationForProject = async (project: ProjectDetails) => {
    if (!project) return;

    const resources = project.resources ?? [];

    const instructions: Part[] = [
      { text: `Project "${project.name}" (ID: ${project.id})` },
      { text: 'Project contents:' },
      ...(project.folders.length > 0
        ? [
            {
              text:
                'Folders:\n' + project.folders.map((f) => `- "${f.name}" (ID: ${f.id})`).join('\n'),
            },
          ]
        : []),
      ...(resources.length > 0
        ? [{ text: 'Files:\n' + resources.map((r) => `- "${r.name}" (ID: ${r.id})`).join('\n') }]
        : []),
    ];

    const instructionPromises = resources.map(formatInstructionForResource);
    const resourceInstructions = await Promise.all(instructionPromises);
    resourceInstructions.forEach((instruction) => instructions.push(...instruction));

    setActiveResources(resources);
    await startConversation(instructions);
  };

  // Reset the AI agent session and clear all state
  const resetAgent = useCallback(() => {
    // Abort any ongoing requests first
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }

    setSession(null);
    setMessages([]);
    setIsReplying(false);
    setIsStartingConversation(false);
    setPendingInstructions([]);
    setActiveResources([]);
    setProcessedFiles(new Map());
    setProcessingFiles(new Set());
  }, [abortController]);

  return {
    startConversationWithResources,
    startConversationForResource,
    startConversationForProject,
    sendMessage,
    updateContextWithNewResources,
    removeResourcesFromContext,
    stopConversation,
    isReplying,
    messages,
    isStartingConversation,
    allowSendMessage: session && !isReplying,
    hasActiveSession: !!session,
    activeResources,
    resetAgent,
  };
};

export default useAiAgent;
