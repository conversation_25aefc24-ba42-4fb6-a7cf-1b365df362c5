/* eslint-disable consistent-return */

import type { Part } from 'firebase/ai';
import type { IMode } from 'src/types/ai-suggestions';
import type { LiveTranscriptionSegment } from 'src/types';

import { throttle } from 'lodash';
import { useRef, useState } from 'react';

import { geminiModel } from 'src/lib/firebase';

const getSystemInstruction = (mode: IMode = 'balanced'): Part[] => {
  const baseInstruction: Part = {
    text: 'You are a helpful and intelligent assistant helping a user conduct qualitative analysis of a meeting transcript.',
  };

  const fallbackInstruction: Part = {
    text: "If there are no follow-up questions to ask based on the transcript, respond with: 'No follow-up questions.'",
  };

  const modeInstructions: Record<IMode, Part[]> = {
    fast: [
      {
        text: 'Generate quick, surface-level follow-up questions that capture immediate reactions or clarifications. Avoid deep reasoning or multi-part questions.',
      },
      {
        text: "Examples: 'Can you clarify X?', 'What do you mean by Y?', 'Who is responsible for Z?'",
      },
    ],
    balanced: [
      {
        text: 'Generate well-rounded follow-up questions that aim to clarify intent, uncover assumptions, or expand on the ideas mentioned. Aim for thoughtful, yet concise questions.',
      },
      {
        text: "Consider the user's goals, business context, and ambiguity in the transcript. Avoid yes/no questions unless necessary.",
      },
      {
        text: "Examples: 'What are the key risks with the proposed solution?', 'How would this impact the next release cycle?', 'What alternatives were considered?'",
      },
    ],
    deep: [
      {
        text: 'Generate in-depth, analytical follow-up questions that explore motivations, trade-offs, conflicts, and systemic impact. Aim to uncover hidden insights or contradictions in the transcript.',
      },
      {
        text: 'Use critical thinking and domain knowledge. Ask about long-term implications, strategy, and deeper rationale behind the statements.',
      },
      {
        text: "Examples: 'What assumptions underpin this decision?', 'How does this align with our long-term vision?', 'Can you elaborate on the challenges mentioned and their root causes?'",
      },
    ],
  };

  return [baseInstruction, ...modeInstructions[mode], fallbackInstruction];
};

const getFollowUpQuestions = async (
  transcript: string,
  systemInstruction: Part[],
  mode: IMode
): Promise<string> => {
  if (!transcript) return 'Transcript is empty.';
  if (!geminiModel) return 'AI model not initialized.';

  try {
    const streamingResponse = await geminiModel.generateContentStream({
      systemInstruction: {
        parts: systemInstruction.length ? systemInstruction : getSystemInstruction(mode),
        role: 'system',
      },
      contents: [
        {
          role: 'user' as const,
          parts: [
            {
              text: transcript,
            },
          ],
        },
      ],
    });
    let fullText = '';

    // Process each chunk of the stream
    for await (const chunk of streamingResponse.stream) {
      const chunkText = chunk.text();
      fullText += chunkText;
    }

    return fullText.trim();
  } catch (error) {
    // console.error('Error generating follow-up questions:', error);
    if (error instanceof Error) {
      // More detailed error handling
      if (error.message?.includes('not found')) {
        return 'Failed: AI model configuration not found. Please ensure Vertex AI API is enabled.';
      } else if (error.message?.includes('permission')) {
        return 'Failed: Permission denied. Check your API key and permissions.';
      } else if (error.message?.includes('quota')) {
        return 'Failed: API quota exceeded. Try again later.';
      }
    }

    return 'Failed to generate follow-up questions.';
  }
};
function processTranscripts(transcripts: LiveTranscriptionSegment[]): string {
  if (!transcripts || transcripts.length === 0) {
    throw new Error('No transcripts provided');
  }
  // Combine all transcripts into a single text with proper spacing
  return transcripts.map((t) => t.text.trim()).join(' ');
}
export function useAiSuggestions() {
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const lastProcessedTranscriptIndexRef = useRef<number>(-1);

  const throttledGetQuestions = useRef(
    throttle(
      async (
        newTranscripts: LiveTranscriptionSegment[],
        startIndex: number,
        mode: IMode
      ): Promise<void> => {
        const allTranscripts = newTranscripts;
        const newTranscriptsOnly = newTranscripts.slice(startIndex);

        const fullContext = processTranscripts(allTranscripts);
        const newContent = processTranscripts(newTranscriptsOnly);

        // console.log('Generating follow-up questions:', {
        //   startIndex,
        //   totalTranscripts: allTranscripts.length,
        //   newTranscriptsCount: newTranscriptsOnly.length,
        //   hasContext: fullContext.length > 0,
        //   hasNewContent: newContent.length > 0,
        // });

        const maxRetries = 3;
        let retryCount = 0;

        const tryGetQuestions = async (): Promise<void> => {
          try {
            setIsLoading(true);
            const questions = await getFollowUpQuestions(fullContext, [], mode);
            console.log('Follow-up questions received:', questions);

            const questionsArray = questions
              .split(/[\n\r]+/)
              .map((q) => q.trim())
              .filter((q) => q.length > 0 && q !== 'No follow-up questions.');

            if (questionsArray.length === 0) {
              throw new Error('No valid questions generated');
            }

            setFollowUpQuestions(questionsArray);
            lastProcessedTranscriptIndexRef.current = newTranscripts.length - 1;
          } catch (error) {
            // console.error('Failed to get questions:', error);

            if (retryCount < maxRetries) {
              retryCount++;
              // console.log(`Retrying (${retryCount}/${maxRetries})...`);
              await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
              return tryGetQuestions();
            }

            // console.log('Max retries reached, keeping existing questions');
          } finally {
            setIsLoading(false);
          }
        };

        return tryGetQuestions();
      },
      3000
    )
  ).current;

  const handleTranscriptsChange = (newTranscripts: LiveTranscriptionSegment[], mode: IMode) => {
    if (newTranscripts.length > lastProcessedTranscriptIndexRef.current + 1) {
      throttledGetQuestions(newTranscripts, lastProcessedTranscriptIndexRef.current + 1, mode);
    }
  };

  return {
    followUpQuestions,
    handleTranscriptsChange,
    isLoading,
  };
}
