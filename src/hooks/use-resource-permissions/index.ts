import { useMemo } from 'react';

import type { UseResourcePermissionsProps, UseResourcePermissionsResult } from './types';

export const useResourcePermissions = ({
  resource,
}: UseResourcePermissionsProps): UseResourcePermissionsResult =>
  useMemo(() => {
    if (!resource?.userPermissions) {
      return {
        canView: false,
        canEdit: false,
        canComment: false,
      };
    }

    return {
      canView: resource.userPermissions.canView ?? false,
      canEdit: resource.userPermissions.canEdit ?? false,
      canComment: resource.userPermissions.canComment ?? false,
    };
  }, [resource?.userPermissions]);

export default useResourcePermissions;
