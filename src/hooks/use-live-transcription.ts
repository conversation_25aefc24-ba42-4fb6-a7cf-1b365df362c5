import type {
  AudioSource,
  LiveTranscriptionState,
  LiveTranscriptionConfig,
  LiveTranscriptionResult,
  LiveTranscriptionSegment,
  UseLiveTranscriptionReturn,
} from 'src/types';

import { v4 as uuidv4 } from 'uuid';
import { useRef, useMemo, useState, useCallback } from 'react';

import { useGetLiveTranscriptionJwtMutation } from 'src/store/api/live-transcription/hooks';

import { toast } from 'src/components/snackbar';

// Optimized constants for better performance
const RECORDING_SAMPLE_RATE = 16_000;
const SPEECHMATICS_RT_URL = 'wss://eu2.rt.speechmatics.com/v2';
const BUFFER_SIZE = 4096; // Reduced for lower latency
const AUDIO_WORKLET_URL = '/audio-worklet-processor.js';

// Transcription optimization constants
const PARTIAL_TRANSCRIPT_THROTTLE = 300; // ms - reduce UI jitter from rapid partial updates
const MAX_SPEAKERS_DEFAULT = 10; // Reduced from 50 for better accuracy
const SPEAKER_SENSITIVITY = 0.7; // Higher sensitivity for better speaker detection
const MAX_DELAY = 2.0; // Balanced delay for better accuracy vs latency

// Audio mixing constants for proper dual-stream handling
const AUDIO_MIX_RATIO = 0.5; // Equal mix of microphone and speaker audio

export const useLiveTranscription = (): UseLiveTranscriptionReturn => {
  const [getLiveTranscriptionJwt] = useGetLiveTranscriptionJwtMutation();

  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedAudioDeviceId, setSelectedAudioDeviceId] = useState<string>('');
  const audioPermissionGrant = useRef<boolean>(false);

  const segmentsRef = useRef<LiveTranscriptionSegment[]>([]);
  const websocketRef = useRef<WebSocket | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // Audio mixing refs for single WebSocket with mixed audio
  const mixerNodeRef = useRef<GainNode | null>(null);
  const microphoneGainRef = useRef<GainNode | null>(null);
  const speakerGainRef = useRef<GainNode | null>(null);

  // Audio recording refs for file export
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const recordingStartTimeRef = useRef<number>(0);

  // Dual stream refs (kept for backward compatibility and state management)
  const speakerWebsocketRef = useRef<WebSocket | null>(null); // Will be same as websocketRef
  const microphoneWebsocketRef = useRef<WebSocket | null>(null); // Will be same as websocketRef
  const speakerMediaStreamRef = useRef<MediaStream | null>(null);
  const speakerAudioContextRef = useRef<AudioContext | null>(null); // Will be same as audioContextRef
  const speakerProcessorRef = useRef<ScriptProcessorNode | null>(null);
  const speakerWorkletNodeRef = useRef<AudioWorkletNode | null>(null);
  const speakerSourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // Add buffering for better performance
  const audioBufferRef = useRef<ArrayBuffer[]>([]);
  const bufferFlushIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const metricsUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioLevelRef = useRef<number>(0);

  // Throttling for partial transcripts to reduce UI jitter
  const partialTranscriptTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingPartialSegmentRef = useRef<{
    segment: LiveTranscriptionSegment;
    audioSource: AudioSource;
  } | null>(null);

  const [state, setState] = useState<LiveTranscriptionState>({
    isConnected: false,
    isRecording: false,
    isTranscribing: false,
    segments: [],
    error: null,
    connectionState: 'disconnected',
    isDualMode: false,
    dualStream: {
      isCapturingScreen: false,
      speakerConnected: false,
      microphoneConnected: false,
      speakerConnectionState: 'disconnected',
      microphoneConnectionState: 'disconnected',
      speakerError: null,
      microphoneError: null,
    },
  });

  // Memoized audio constraints for better performance
  const audioConstraints = useMemo(
    () => ({
      sampleRate: RECORDING_SAMPLE_RATE,
      channelCount: 1,
      echoCancellation: true, // Keep for microphone - removes echo
      noiseSuppression: true, // Good for microphone - removes background noise
      autoGainControl: true, // Good for microphone - normalizes volume
      latency: 0, // Request lowest latency
    }),
    []
  );

  const speakerConstraints = useMemo(
    () => ({
      sampleRate: RECORDING_SAMPLE_RATE,
      channelCount: 1,
      echoCancellation: false, // Don't alter screen audio
      noiseSuppression: false, // Preserve original screen audio quality
      autoGainControl: false, // Don't auto-adjust screen audio levels
      suppressLocalAudioPlayback: false,
    }),
    []
  );

  const flushAudioBuffer = useCallback(() => {
    // In dual mode, use microphoneWebsocketRef; in single mode, use websocketRef
    const targetWebSocket = microphoneWebsocketRef.current || websocketRef.current;

    if (audioBufferRef.current.length > 0 && targetWebSocket?.readyState === WebSocket.OPEN) {
      // Send all buffered audio data
      for (const buffer of audioBufferRef.current) {
        targetWebSocket.send(buffer);
      }

      audioBufferRef.current = [];
    }
  }, []);

  // Helper function to format speaker IDs consistently
  const formatSpeakerId = useCallback((speakerId: string | undefined): string | undefined => {
    if (!speakerId) return undefined;

    // Speechmatics returns speaker IDs in various formats, normalize them to S1, S2, etc.
    // Handle formats like "S1", "Speaker 1", "speaker_1", etc.
    const speakerMatch = speakerId.match(/(?:speaker[_\s]*|s)(\d+)/i);
    if (speakerMatch) {
      return `S${speakerMatch[1]}`;
    }

    // If no pattern matches, return as is
    return speakerId;
  }, []);

  const updateSegmentsImmediate = useCallback(
    (newSegment: LiveTranscriptionSegment, audioSource?: AudioSource) => {
      // Set audio source if provided
      if (audioSource) {
        newSegment.audioSource = audioSource;
      }

      // Format speaker ID consistently
      newSegment.speakerId = formatSpeakerId(newSegment.speakerId);

      if (newSegment.isFinal) {
        // For final segments, check if we already have a similar segment to avoid duplicates
        const existingSegments = segmentsRef.current.filter((segment) => segment.isFinal);
        const isDuplicate = existingSegments.some((existing) => {
          const textSimilarity = existing.text.trim() === newSegment.text.trim();
          const timeSimilarity = Math.abs(existing.startTime - newSegment.startTime) < 1; // Within 1 second
          const sameSpeaker = existing.speakerId === newSegment.speakerId;
          const sameSource = existing.audioSource === newSegment.audioSource;

          return textSimilarity && timeSimilarity && sameSpeaker && sameSource;
        });

        if (!isDuplicate) {
          // Remove any partial segments from the same source that might be replaced by this final segment
          const filteredSegments = segmentsRef.current.filter((segment) => {
            if (segment.isFinal) return true; // Keep all final segments
            if (segment.audioSource !== audioSource) return true; // Keep partials from other sources

            // Remove partial segments that are likely replaced by this final segment
            const isReplaced =
              segment.text.trim() === newSegment.text.trim() ||
              newSegment.text.includes(segment.text.trim());
            return !isReplaced;
          });

          segmentsRef.current = [...filteredSegments, newSegment];
        }
      } else {
        // For partial segments, replace the last partial segment from the same source
        const finalSegments = segmentsRef.current.filter((segment) => segment.isFinal);
        const otherPartialSegments = segmentsRef.current.filter(
          (segment) => !segment.isFinal && segment.audioSource !== audioSource
        );
        segmentsRef.current = [...finalSegments, ...otherPartialSegments, newSegment];
      }

      setState((prev) => ({
        ...prev,
        segments: segmentsRef.current,
      }));
    },
    [formatSpeakerId]
  );

  // Throttled update for partial transcripts to reduce UI jitter
  const updatePartialSegmentThrottled = useCallback(
    (newSegment: LiveTranscriptionSegment, audioSource: AudioSource) => {
      // Store the pending partial segment
      pendingPartialSegmentRef.current = { segment: newSegment, audioSource };

      // Clear existing timeout
      if (partialTranscriptTimeoutRef.current) {
        clearTimeout(partialTranscriptTimeoutRef.current);
      }

      // Set new timeout to update after throttle period
      partialTranscriptTimeoutRef.current = setTimeout(() => {
        const pending = pendingPartialSegmentRef.current;
        if (pending) {
          updateSegmentsImmediate(pending.segment, pending.audioSource);
          pendingPartialSegmentRef.current = null;
        }
      }, PARTIAL_TRANSCRIPT_THROTTLE);
    },
    [updateSegmentsImmediate]
  );

  const updateSegments = useCallback(
    (newSegment: LiveTranscriptionSegment, audioSource?: AudioSource) => {
      if (newSegment.isFinal) {
        // Final segments are updated immediately
        updateSegmentsImmediate(newSegment, audioSource);
      } else {
        // Partial segments are throttled to reduce UI jitter
        updatePartialSegmentThrottled(newSegment, audioSource!);
      }
    },
    [updateSegmentsImmediate, updatePartialSegmentThrottled]
  );

  const cleanupAudioResources = useCallback(() => {
    // Clear buffer flush interval
    if (bufferFlushIntervalRef.current) {
      clearInterval(bufferFlushIntervalRef.current);
      bufferFlushIntervalRef.current = null;
    }

    // Clear metrics update interval
    if (metricsUpdateIntervalRef.current) {
      clearInterval(metricsUpdateIntervalRef.current);
      metricsUpdateIntervalRef.current = null;
    }

    // Clear partial transcript throttling timeout
    if (partialTranscriptTimeoutRef.current) {
      clearTimeout(partialTranscriptTimeoutRef.current);
      partialTranscriptTimeoutRef.current = null;
    }

    // Cleanup audio mixing nodes
    if (mixerNodeRef.current) {
      mixerNodeRef.current.disconnect();
      mixerNodeRef.current = null;
    }
    if (microphoneGainRef.current) {
      microphoneGainRef.current.disconnect();
      microphoneGainRef.current = null;
    }
    if (speakerGainRef.current) {
      speakerGainRef.current.disconnect();
      speakerGainRef.current = null;
    }

    // Clear audio buffer
    audioBufferRef.current = [];
    pendingPartialSegmentRef.current = null;

    if (workletNodeRef.current) {
      workletNodeRef.current.disconnect();
      workletNodeRef.current = null;
    }
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }
    if (sourceRef.current) {
      sourceRef.current.disconnect();
      sourceRef.current = null;
    }
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach((track) => track.stop());
      mediaStreamRef.current = null;
    }
  }, []);

  const cleanupWebSocket = useCallback(() => {
    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = null;
    }
  }, []);

  // Single WebSocket setup for mixed audio
  const setupWebSocket = useCallback(
    (jwt: string, config: LiveTranscriptionConfig) =>
      new Promise<void>((resolve, reject) => {
        try {
          const ws = new WebSocket(`${SPEECHMATICS_RT_URL}?jwt=${jwt}`);
          ws.binaryType = 'arraybuffer';

          ws.onopen = () => {
            // Send StartRecognition message with optimized configuration
            const startMessage = {
              message: 'StartRecognition',
              audio_format: {
                type: 'raw',
                encoding: 'pcm_f32le',
                sample_rate: RECORDING_SAMPLE_RATE,
              },
              transcription_config: {
                language: config.language,
                diarization: 'speaker',
                operating_point: 'enhanced',
                max_delay_mode: 'flexible',
                max_delay: MAX_DELAY,
                enable_partials: true,
                enable_entities: true,
                speaker_diarization_config: {
                  max_speakers: MAX_SPEAKERS_DEFAULT,
                  speaker_sensitivity: SPEAKER_SENSITIVITY,
                  prefer_current_speaker: true,
                },
              },
            };

            ws.send(JSON.stringify(startMessage));
          };

          ws.onmessage = (event) => {
            try {
              const message = JSON.parse(event.data);

              switch (message.message) {
                case 'RecognitionStarted':
                  setState((prev) => ({
                    ...prev,
                    isConnected: true,
                    connectionState: 'connected',
                  }));
                  resolve();
                  break;

                case 'AddPartialTranscript':
                  if (message.metadata?.transcript) {
                    const speakerId = message.results?.find(
                      (result: any) => result.alternatives?.[0]?.speaker
                    )?.alternatives?.[0]?.speaker;

                    const segment: LiveTranscriptionSegment = {
                      id: uuidv4(),
                      text: message.metadata.transcript,
                      confidence: 0.8,
                      startTime: message.metadata.start_time || Date.now() / 1000,
                      endTime: message.metadata.end_time || Date.now() / 1000,
                      isFinal: false,
                      speakerId: speakerId || undefined,
                      timestamp: new Date(),
                      audioSource: 'microphone', // Mixed audio treated as microphone source
                    };
                    updateSegments(segment, 'microphone');
                  }
                  break;

                case 'AddTranscript':
                  if (message.metadata?.transcript) {
                    const avgConfidence = message.results?.length
                      ? message.results.reduce(
                        (sum: number, result: any) =>
                          sum + (result.alternatives?.[0]?.confidence || 0),
                        0
                      ) / message.results.length
                      : 0.9;

                    const speakerId = message.results?.find(
                      (result: any) => result.alternatives?.[0]?.speaker
                    )?.alternatives?.[0]?.speaker;

                    const segment: LiveTranscriptionSegment = {
                      id: uuidv4(),
                      text: message.metadata.transcript,
                      confidence: avgConfidence,
                      startTime: message.metadata.start_time || Date.now() / 1000,
                      endTime: message.metadata.end_time || Date.now() / 1000,
                      isFinal: true,
                      speakerId: speakerId || undefined,
                      timestamp: new Date(),
                      audioSource: 'microphone', // Mixed audio treated as microphone source
                    };
                    updateSegments(segment, 'microphone');
                  }
                  break;

                case 'AudioAdded':
                  break;

                case 'Info':
                  break;

                case 'Warning':
                  console.warn('Speechmatics warning:', message);
                  break;

                case 'Error':
                  console.error('Speechmatics error:', message);
                  reject(new Error(message.reason || 'Unknown error'));
                  break;

                default:
                  break;
              }
            } catch (error) {
              console.error('Error parsing WebSocket message:', error);
            }
          };

          ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            reject(new Error('WebSocket connection failed'));
          };

          ws.onclose = (event) => {
            setState((prev) => ({
              ...prev,
              isConnected: false,
              connectionState: 'disconnected',
            }));
          };

          // Store WebSocket reference
          websocketRef.current = ws;
        } catch (error) {
          reject(error);
        }
      }),
    [updateSegments]
  );

  // NEW: Setup mixed audio capture (single WebSocket with combined audio)
  const setupMixedAudioCapture = useCallback(async () => {
    try {
      // Get microphone stream
      const constraints = selectedAudioDeviceId
        ? { ...speakerConstraints, deviceId: { exact: selectedAudioDeviceId } }
        : audioConstraints;

      const microphoneStream = await navigator.mediaDevices.getUserMedia({ audio: constraints });
      mediaStreamRef.current = microphoneStream;

      // Create shared audio context
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: RECORDING_SAMPLE_RATE,
        latencyHint: 'interactive',
      });
      audioContextRef.current = audioContext;
      speakerAudioContextRef.current = audioContext; // Share the same context

      // Create audio mixer and gain nodes
      const mixer = audioContext.createGain();
      const microphoneGain = audioContext.createGain();
      const speakerGain = audioContext.createGain();

      mixerNodeRef.current = mixer;
      microphoneGainRef.current = microphoneGain;
      speakerGainRef.current = speakerGain;

      // Set equal mixing levels
      microphoneGain.gain.value = AUDIO_MIX_RATIO;
      speakerGain.gain.value = AUDIO_MIX_RATIO;

      // Connect microphone to mixer
      const microphoneSource = audioContext.createMediaStreamSource(microphoneStream);
      sourceRef.current = microphoneSource;
      microphoneSource.connect(microphoneGain);
      microphoneGain.connect(mixer);

      // Try to get speaker audio and connect it to mixer
      let speakerConnected = false;
      try {
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: 1, height: 1 },
          audio: {
            sampleRate: RECORDING_SAMPLE_RATE,
            channelCount: 1,
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
          },
        });

        const audioTracks = screenStream.getAudioTracks();
        if (audioTracks.length > 0) {
          speakerMediaStreamRef.current = screenStream;
          const speakerSource = audioContext.createMediaStreamSource(screenStream);
          speakerSourceRef.current = speakerSource;
          speakerSource.connect(speakerGain);
          speakerGain.connect(mixer);
          speakerConnected = true;

          // Handle stream end
          screenStream.getTracks().forEach((track) => {
            track.addEventListener('ended', () => {
              // Disconnect speaker from mixer but keep microphone running
              speakerSource.disconnect();
              setState((prev) => ({
                ...prev,
                dualStream: {
                  ...prev.dualStream!,
                  isCapturingScreen: false,
                  speakerConnected: false,
                },
              }));
            });
          });
        }
      } catch {
        // Speaker audio failed, continue with microphone only
      }

      // Setup audio processing from the mixed output
      try {
        await audioContext.audioWorklet.addModule(AUDIO_WORKLET_URL);

        const workletNode = new AudioWorkletNode(audioContext, 'audio-worklet-processor');
        workletNodeRef.current = workletNode;

        workletNode.port.onmessage = (event) => {
          if (event.data.type === 'audioData') {
            if (typeof event.data.audioLevel === 'number') {
              audioLevelRef.current = event.data.audioLevel;
            }

            // Send mixed audio to single WebSocket
            const targetWebSocket = websocketRef.current;
            if (targetWebSocket?.readyState === WebSocket.OPEN) {
              targetWebSocket.send(event.data.data);
            } else {
              audioBufferRef.current.push(event.data.data);
            }
          }
        };      // Connect mixer to worklet (this sends combined audio)
        mixer.connect(workletNode);
      } catch {
        // Fallback to ScriptProcessorNode
        const processor = audioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
        processorRef.current = processor;

        processor.onaudioprocess = (event) => {
          const targetWebSocket = websocketRef.current;
          if (targetWebSocket?.readyState === WebSocket.OPEN) {
            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Calculate audio level
            let sum = 0;
            for (let i = 0; i < inputData.length; i++) {
              sum += inputData[i] * inputData[i];
            }
            audioLevelRef.current = Math.sqrt(sum / inputData.length);

            // Send mixed audio
            const buffer = new ArrayBuffer(inputData.length * 4);
            const view = new Float32Array(buffer);
            view.set(inputData);
            targetWebSocket.send(buffer);
          }
        };

        mixer.connect(processor);
        processor.connect(audioContext.destination);
      }

      // Start recording mixed audio for file export
      try {
        const destination = audioContext.createMediaStreamDestination();
        mixer.connect(destination);

        const mediaRecorder = new MediaRecorder(destination.stream, {
          mimeType: 'audio/webm;codecs=opus',
        });

        mediaRecorderRef.current = mediaRecorder;
        recordedChunksRef.current = [];
        recordingStartTimeRef.current = Date.now();

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            recordedChunksRef.current.push(event.data);
          }
        };

        mediaRecorder.start(1000); // Record in 1-second chunks
      } catch (recordingError) {
        console.warn('Failed to start audio recording for export:', recordingError);
      }

      // Update state to reflect what was actually connected
      setState((prev) => ({
        ...prev,
        isRecording: true,
        dualStream: {
          ...prev.dualStream!,
          isCapturingScreen: speakerConnected,
          speakerConnected,
          microphoneConnected: true,
          speakerConnectionState: speakerConnected ? 'connected' : 'disconnected',
          microphoneConnectionState: 'connected',
        },
      }));

      return { audioContext, speakerConnected };
    } catch (error) {
      console.error('Error setting up mixed audio capture:', error);
      throw new Error('Failed to setup audio capture');
    }
  }, [selectedAudioDeviceId, audioConstraints, speakerConstraints]);

  const handleStartTranscription = useCallback(
    async (config: LiveTranscriptionConfig) => {
      try {
        setState((prev) => ({
          ...prev,
          isDualMode: true,
          error: null,
          connectionState: 'connecting',
          dualStream: {
            ...prev.dualStream!,
            speakerConnectionState: 'connecting',
            microphoneConnectionState: 'connecting',
          },
        }));

        // Get single JWT token for mixed audio stream
        const jwtResponse = await getLiveTranscriptionJwt({}).unwrap();
        if (!jwtResponse.jwt) {
          throw new Error('Failed to get JWT token');
        }

        // Setup single WebSocket connection
        await setupWebSocket(jwtResponse.jwt, config);

        // Setup mixed audio capture (microphone + speaker)
        const { speakerConnected } = await setupMixedAudioCapture();

        // Set dual WebSocket refs to point to the same single WebSocket for compatibility
        microphoneWebsocketRef.current = websocketRef.current;
        speakerWebsocketRef.current = websocketRef.current;

        setState((prev) => ({
          ...prev,
          isRecording: true,
          isTranscribing: true,
          connectionState: 'connected',
          isConnected: true,
        }));

        if (speakerConnected) {
          toast.success('Live transcription started (capturing all audio)');
        } else {
          toast.warning('Live transcription started (microphone only - tab audio not available)');
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to start transcription';

        setState((prev) => ({
          ...prev,
          isDualMode: false,
          error: errorMessage,
          connectionState: 'error',
          isRecording: false,
          isTranscribing: false,
          dualStream: {
            ...prev.dualStream!,
            speakerConnectionState: 'error',
            microphoneConnectionState: 'error',
            speakerError: errorMessage,
            microphoneError: errorMessage,
          },
        }));

        cleanupAudioResources();
        cleanupWebSocket();
        toast.error(`Failed to start transcription: ${errorMessage}`);
        throw error;
      }
    },
    [
      getLiveTranscriptionJwt,
      setupWebSocket,
      setupMixedAudioCapture,
      cleanupAudioResources,
      cleanupWebSocket,
    ]
  );

  const handleStopTranscription = useCallback(async (): Promise<LiveTranscriptionResult> => {
    const endTime = new Date();
    let audioFile: Blob | null = null;

    try {
      // Ensure any pending partial segments are processed before stopping
      if (partialTranscriptTimeoutRef.current) {
        clearTimeout(partialTranscriptTimeoutRef.current);
        const pending = pendingPartialSegmentRef.current;
        if (pending) {
          updateSegmentsImmediate(pending.segment, pending.audioSource);
          pendingPartialSegmentRef.current = null;
        }
      }

      // Stop recording first to capture all audio
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        await new Promise<void>((resolve) => {
          mediaRecorderRef.current!.onstop = () => {
            // Create audio file from recorded chunks
            if (recordedChunksRef.current.length > 0) {
              audioFile = new Blob(recordedChunksRef.current, { type: 'audio/webm;codecs=opus' });
            }
            resolve();
          };
          mediaRecorderRef.current!.stop();
        });
      }

      // Clear buffer flush interval
      if (bufferFlushIntervalRef.current) {
        clearInterval(bufferFlushIntervalRef.current);
        bufferFlushIntervalRef.current = null;
      }

      // Send EndOfStream messages to both WebSockets if they exist
      if (microphoneWebsocketRef.current?.readyState === WebSocket.OPEN) {
        flushAudioBuffer();
        const endMessage = {
          message: 'EndOfStream',
          last_seq_no: 1,
        };
        microphoneWebsocketRef.current.send(JSON.stringify(endMessage));
      }

      if (speakerWebsocketRef.current?.readyState === WebSocket.OPEN) {
        flushAudioBuffer();
        const endMessage = {
          message: 'EndOfStream',
          last_seq_no: 1,
        };
        speakerWebsocketRef.current.send(JSON.stringify(endMessage));
      }

      // Also handle legacy single WebSocket
      if (websocketRef.current?.readyState === WebSocket.OPEN) {
        flushAudioBuffer();
        const endMessage = {
          message: 'EndOfStream',
          last_seq_no: 1,
        };
        websocketRef.current.send(JSON.stringify(endMessage));
      }

      // Wait for final transcripts to arrive after EndOfStream
      // This is crucial for Speechmatics to send any final transcripts
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second for final transcripts

      // IMPORTANT: Capture transcript data BEFORE cleanup operations
      // Prepare transcript data - use all segments including partial ones
      // First try to get final segments, if none exist, use all segments
      const conversation = [...segmentsRef.current].reduce<Array<{
        sentence: string;
        startTime: number;
        endTime: number;
        speakerId: string;
        content: string;
      }>>((acc, segment) => {
        if (!segment.text.trim()) return acc;

        if (segment.isFinal) {
          // End of sentence - create final entry
          const existingEntry = acc[acc.length - 1];
          if (existingEntry && !existingEntry.sentence.includes(segment.text.trim())) {
            // Combine with existing partial segments
            existingEntry.sentence += (existingEntry.sentence ? ' ' : '') + segment.text.trim();
            existingEntry.content = existingEntry.sentence;
            existingEntry.endTime = segment.endTime;
          } else {
            // Create new entry for this final segment
            acc.push({
              sentence: segment.text.trim(),
              startTime: segment.startTime,
              endTime: segment.endTime,
              speakerId: segment.speakerId || 'Unknown',
              content: segment.text.trim()
            });
          }
        } else {
          // Partial segment - start new sentence or update existing
          const existingEntry = acc[acc.length - 1];
          if (existingEntry && existingEntry.speakerId === (segment.speakerId || 'Unknown')) {
            // Same speaker, update existing entry
            existingEntry.sentence = segment.text.trim();
            existingEntry.content = segment.text.trim();
            existingEntry.endTime = segment.endTime;
          } else {
            // Different speaker or first segment, create new entry
            acc.push({
              sentence: segment.text.trim(),
              startTime: segment.startTime,
              endTime: segment.endTime,
              speakerId: segment.speakerId || 'Unknown',
              content: segment.text.trim()
            });
          }
        }

        return acc;
      }, []);

      console.log('Final conversation data:', conversation);
      // Calculate duration
      const duration = recordingStartTimeRef.current
        ? (endTime.getTime() - recordingStartTimeRef.current) / 1000
        : 0;

      // Cleanup audio resources
      cleanupAudioResources();

      // Cleanup speaker audio resources
      if (speakerWorkletNodeRef.current) {
        speakerWorkletNodeRef.current.disconnect();
        speakerWorkletNodeRef.current = null;
      }
      if (speakerProcessorRef.current) {
        speakerProcessorRef.current.disconnect();
        speakerProcessorRef.current = null;
      }
      if (speakerSourceRef.current) {
        speakerSourceRef.current.disconnect();
        speakerSourceRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        speakerAudioContextRef.current.close();
        speakerAudioContextRef.current = null;
      }

      // Stop screen capture if active
      if (speakerMediaStreamRef.current) {
        speakerMediaStreamRef.current.getTracks().forEach((track) => track.stop());
        speakerMediaStreamRef.current = null;
      }

      // Clear audio buffer
      audioBufferRef.current = [];

      // Close WebSockets after a short delay
      setTimeout(() => {
        cleanupWebSocket();
        if (speakerWebsocketRef.current) {
          speakerWebsocketRef.current.close();
          speakerWebsocketRef.current = null;
        }
        if (microphoneWebsocketRef.current) {
          microphoneWebsocketRef.current.close();
          microphoneWebsocketRef.current = null;
        }
      }, 500);

      setState((prev) => ({
        ...prev,
        isRecording: false,
        isTranscribing: false,
        connectionState: 'disconnected',
        isConnected: false,
        isDualMode: false,
        dualStream: {
          ...prev.dualStream!,
          isCapturingScreen: false,
          speakerConnected: false,
          microphoneConnected: false,
          speakerConnectionState: 'disconnected',
          microphoneConnectionState: 'disconnected',
          speakerError: null,
          microphoneError: null,
        },
      }));

      // Reset recording refs
      mediaRecorderRef.current = null;
      recordedChunksRef.current = [];
      recordingStartTimeRef.current = 0;

      // Return result
      const result: LiveTranscriptionResult = {
        audioFile: audioFile || new Blob([], { type: 'audio/webm;codecs=opus' }),
        transcriptData: conversation,
        duration,
        startTime: new Date(recordingStartTimeRef.current || endTime.getTime() - duration * 1000),
        endTime,
      };

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop transcription';

      setState((prev) => ({
        ...prev,
        error: errorMessage,
      }));

      // Force cleanup on error
      cleanupAudioResources();
      cleanupWebSocket();

      // Reset recording refs
      mediaRecorderRef.current = null;
      recordedChunksRef.current = [];
      recordingStartTimeRef.current = 0;

      toast.error(`Failed to stop transcription: ${errorMessage}`);

      // Return empty result on error
      const result: LiveTranscriptionResult = {
        audioFile: new Blob([], { type: 'audio/webm;codecs=opus' }),
        transcriptData: [],
        duration: 0,
        startTime: new Date(),
        endTime: new Date(),
      };

      return result;
    }
  }, [cleanupAudioResources, cleanupWebSocket, flushAudioBuffer, updateSegmentsImmediate]);

  const clearTranscription = useCallback(() => {
    segmentsRef.current = [];
    setState((prev) => ({
      ...prev,
      segments: [],
      error: null,
    }));
  }, []);

  const exportTranscription = useCallback(() => {
    const finalSegments = segmentsRef.current.filter((segment) => segment.isFinal);
    return finalSegments
      .map((segment) => segment.text)
      .filter((text) => text.trim())
      .join(' ');
  }, []);

  const askForMicrophonePermission = useCallback(
    (silently = true) => {
      if (audioPermissionGrant.current) {
        return Promise.resolve(true);
      }
      return new Promise<boolean>((resolve) => {
        navigator.mediaDevices
          .getUserMedia({ audio: audioConstraints })
          .then(() => {
            audioPermissionGrant.current = true;
            resolve(true);
          })
          .catch((_error) => {
            audioPermissionGrant.current = false;
            if (!silently)
              toast.error(
                'Microphone access denied. Please allow microphone access in your browser settings.'
              );
            resolve(false);
          });
      });
    },
    [audioConstraints]
  );

  const switchAudioDevice = useCallback(
    async (deviceId: string) => {
      if (!audioPermissionGrant.current) {
        const granted = await askForMicrophonePermission(false);
        if (!granted) {
          throw new Error('Microphone access denied');
        }
      }

      setSelectedAudioDeviceId(deviceId);

      try {
        const wasRecording = state.isRecording;

        // If we're currently recording, we need to reconnect the audio pipeline
        if (wasRecording && audioContextRef.current) {
          // Disconnect current audio source but keep the WebSocket connection
          if (sourceRef.current) {
            sourceRef.current.disconnect();
            sourceRef.current = null;
          }

          // Stop current media stream
          if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach((track) => track.stop());
            mediaStreamRef.current = null;
          }

          // Get new media stream with selected device
          const constraints = {
            ...audioConstraints,
            deviceId: { exact: deviceId },
          };

          const stream = await navigator.mediaDevices.getUserMedia({ audio: constraints });
          mediaStreamRef.current = stream;

          // Reconnect audio processing pipeline
          const source = audioContextRef.current.createMediaStreamSource(stream);
          sourceRef.current = source;

          // Reconnect to existing worklet or processor
          if (workletNodeRef.current) {
            source.connect(workletNodeRef.current);
          } else if (processorRef.current) {
            source.connect(processorRef.current);
          }
        } else {
          // Not recording, just switch the device for future use
          if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach((track) => track.stop());
            mediaStreamRef.current = null;
          }

          const constraints = {
            ...audioConstraints,
            deviceId: { exact: deviceId },
          };

          const stream = await navigator.mediaDevices.getUserMedia({ audio: constraints });
          mediaStreamRef.current = stream;

          // Stop the test stream since we're not recording
          stream.getTracks().forEach((track) => track.stop());
          mediaStreamRef.current = null;
        }

        // Update devices list
        const devices = await navigator.mediaDevices.enumerateDevices();
        setAudioDevices(devices.filter((device) => device.kind === 'audioinput'));
      } catch {
        toast.error('Failed to switch audio device');
        throw new Error('Failed to switch audio device');
      }
    },
    [askForMicrophonePermission, audioConstraints, state.isRecording]
  );

  const bootstrap = useCallback(async () => {
    try {
      // Request microphone permissions
      const stream = await navigator.mediaDevices.getUserMedia({ audio: audioConstraints });
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputDevices = devices.filter((device) => device.kind === 'audioinput');

      setAudioDevices(audioInputDevices);
      audioPermissionGrant.current = true;
      stream.getTracks().forEach((track) => track.stop());

      // Set default audio device if available
      const defaultDeviceId = stream.getAudioTracks()[0]?.getSettings().deviceId || '';
      if (defaultDeviceId) {
        setSelectedAudioDeviceId(defaultDeviceId);
      }
    } catch {
      audioPermissionGrant.current = false;
      askForMicrophonePermission(false);
    }
  }, [askForMicrophonePermission, audioConstraints]);

  // Get current audio level for monitoring
  const getAudioLevel = useCallback(() => audioLevelRef.current, []);

  // Dual stream methods
  const getSegmentsBySource = useCallback((source?: AudioSource) => {
    if (!source) return segmentsRef.current;
    return segmentsRef.current.filter((segment) => segment.audioSource === source);
  }, []);

  const exportTranscriptionBySource = useCallback(
    (source?: AudioSource) => {
      const segments = getSegmentsBySource(source);
      const finalSegments = segments.filter((segment) => segment.isFinal);
      return finalSegments
        .map((segment) => segment.text)
        .filter((text) => text.trim())
        .join(' ');
    },
    [getSegmentsBySource]
  );

  return {
    state,
    audioDevices,
    selectedAudioDeviceId,
    startTranscription: handleStartTranscription,
    stopTranscription: handleStopTranscription,
    clearTranscription,
    exportTranscription,
    bootstrap,
    switchAudioDevice,
    getAudioLevel,
    getSegmentsBySource,
    exportTranscriptionBySource,
  };
};
