import { paths } from 'src/routes/paths';

import packageJson from '../package.json';

// ----------------------------------------------------------------------

export type ConfigValue = {
  appName: string;
  appVersion: string;
  aidaApiUrl: string;
  assetsDir: string;
  auth: {
    redirectPath: string;
  };
  posthog: {
    host: string;
    key: string;
  };
  firebase: {
    appId: string;
    apiKey: string;
    projectId: string;
    authDomain: string;
    storageBucket: string;
    measurementId: string;
    messagingSenderId: string;
    geminiModel: string;
  };
  cloudLabUrl: string;
  sentry: {
    dsn: string;
  };
};

// ----------------------------------------------------------------------

export const CONFIG: ConfigValue = {
  appName: 'Beings',
  appVersion: packageJson.version,
  aidaApiUrl: import.meta.env.VITE_AIDA_API_URL ?? '',
  assetsDir: import.meta.env.VITE_ASSETS_DIR ?? '',
  auth: {
    redirectPath: paths.project.root,
  },
  posthog: {
    host: import.meta.env.VITE_POSTHOG_HOST ?? '',
    key: import.meta.env.VITE_POSTHOG_KEY ?? '',
  },
  /**
   * Firebase
   */
  firebase: {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY ?? '',
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN ?? '',
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID ?? '',
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET ?? '',
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID ?? '',
    appId: import.meta.env.VITE_FIREBASE_APPID ?? '',
    measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID ?? '',
    geminiModel: 'gemini-2.5-pro',
  },
  cloudLabUrl: import.meta.env.VITE_CLOUD_LAB_URL ?? '',
  sentry: {
    dsn: 'https://<EMAIL>/4509015299850240',
  },
};
