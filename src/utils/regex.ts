export const URL_REGEX =
  /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\\-\\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/;

// Meeting URL validation patterns
export const GOOGLE_MEET_REGEX = /^https:\/\/(meet\.google\.com\/[a-z-]+)$/i;
export const ZOOM_REGEX = /^https:\/\/([a-zA-Z0-9-]+\.)*zoom\.(us|com)\/[js]\/[0-9]+(\?pwd=[a-zA-Z0-9._-]+)?$/i;
export const MS_TEAMS_REGEX = /^https:\/\/(teams\.microsoft\.com\/l\/meetup-join\/[a-zA-Z0-9%:@._-]+(\/0)?(\?context=[a-zA-Z0-9%._-]+)?|teams\.live\.com\/meet\/[0-9]+(\?p=[a-zA-Z0-9]+)?)$/i;

// Meeting URL format examples
export const MEETING_URL_FORMATS = {
  'google-meet': 'meet.google.com/abc-defg-hij',
  'zoom': 'zoom.us/j/123456789?pwd=abc123.xyz or subdomain.zoom.us/j/123456789',
  'teams': 'teams.microsoft.com/l/meetup-join/19:meeting_abc123@thread.v2/0?context={"Tid":"123","Oid":"456"} or teams.live.com/meet/123456789'
} as const;

export const isValidMeetingUrl = (url: string): { isValid: boolean; platform: string | null } => {
  if (GOOGLE_MEET_REGEX.test(url)) {
    return { isValid: true, platform: 'google-meet' };
  }
  if (ZOOM_REGEX.test(url)) {
    return { isValid: true, platform: 'zoom' };
  }
  if (MS_TEAMS_REGEX.test(url)) {
    return { isValid: true, platform: 'teams' };
  }
  return { isValid: false, platform: null };
};
