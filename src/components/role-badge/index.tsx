import type { Theme, SxProps } from '@mui/material/styles';

import { Chip } from '@mui/material';

import { ProjectRole } from 'src/store/api/projects';

export interface RoleBadgeProps {
  role: ProjectRole | string;
  sx?: SxProps<Theme>;
}

export function RoleBadge({ role, sx }: RoleBadgeProps) {
  const getRoleConfig = (roleValue: string) => {
    switch (roleValue) {
      case ProjectRole.EDITOR:
        return {
          label: 'Editor',
          color: 'success' as const,
          variant: 'filled' as const,
        };
      case ProjectRole.COMMENTER:
        return {
          label: 'Commenter',
          color: 'info' as const,
          variant: 'filled' as const,
        };
      case ProjectRole.VIEWER:
        return {
          label: 'Viewer',
          color: 'default' as const,
          variant: 'outlined' as const,
        };
      default:
        return {
          label: roleValue,
          color: 'default' as const,
          variant: 'outlined' as const,
        };
    }
  };

  const config = getRoleConfig(role);

  return (
    <Chip
      label={config.label}
      color={config.color}
      variant={config.variant}
      size="small"
      sx={{
        fontSize: '0.75rem',
        height: 24,
        ...sx,
      }}
    />
  );
}

export default RoleBadge;
