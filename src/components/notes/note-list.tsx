import type { ProjectRole } from 'src/store/api/projects';

import { Button } from '@mui/material';
import Stack from '@mui/material/Stack';
import AddRoundedIcon from '@mui/icons-material/AddRounded';

import { NoteItem } from './note-item';
import { EmptyContent } from '../empty-content';

import type { NoteItem as NoteItemType } from './types';

interface NoteListProps {
  notes: NoteItemType[];
  onSelectNote: (note: NoteItemType) => void;
  onDelete?: (noteId: string) => Promise<void>;
  onAddNote?: () => void;
  isCanDeleteResource?: boolean;
  role?: ProjectRole | string;
  isOwner?: boolean;
  currentUserId?: string;
}

export function NoteList({
  notes,
  onSelectNote,
  onDelete,
  onAddNote,
  isCanDeleteResource,
  role,
  isOwner = false,
  currentUserId = '',
}: NoteListProps) {
  if (!notes.length) {
    return (
      <EmptyContent
        title="Empty notes"
        description={isCanDeleteResource ? 'Click the button below to add a note' : ''}
        action={
          onAddNote ? (
            <Button startIcon={<AddRoundedIcon />} onClick={onAddNote} sx={{ mt: 1 }}>
              Add Note
            </Button>
          ) : null
        }
      />
    );
  }
  return (
    <Stack spacing={2}>
      {notes.map((note) => (
        <NoteItem
          key={note.id}
          item={note}
          onSelect={onSelectNote}
          onDelete={onDelete}
          isCanDeleteResource={isCanDeleteResource}
          role={role}
          isOwner={isOwner}
          currentUserId={currentUserId}
        />
      ))}
    </Stack>
  );
}
