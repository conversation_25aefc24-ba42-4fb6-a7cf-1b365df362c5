import type { ProjectRole } from 'src/store/api/projects';

import { Notes } from './notes';
import { ContentDialog } from '../content-panel';

// ----------------------------------------------------------------------

interface NoteDialogProps {
  open: boolean;
  onClose: () => void;
  isCanEdit?: boolean;
  role?: ProjectRole | string;
  isOwner?: boolean;
  currentUserId?: string;
}

export function NoteDialog({
  open,
  onClose,
  isCanEdit = true,
  role,
  isOwner = false,
  currentUserId = '',
}: NoteDialogProps) {
  return (
    <ContentDialog title="Notes" open={open} onClose={onClose}>
      <Notes
        isOpen={open}
        onClose={onClose}
        isCanEdit={isCanEdit}
        role={role}
        isOwner={isOwner}
        currentUserId={currentUserId}
      />
    </ContentDialog>
  );
}
