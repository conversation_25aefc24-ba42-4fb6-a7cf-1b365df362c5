import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { useGetProjectsQuery } from 'src/store/api/projects';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

import { LoadingScreen } from 'src/components/loading-screen';

const ProjectsView: React.FC = () => {
  const router = useRouter();
  const { data: projects, isLoading } = useGetProjectsQuery({});
  const lastViewedProjectId = useSelector(selectLastViewedProjectId);

  const isEmpty = !projects || projects.length === 0;

  useEffect(() => {
    // For new layout, we will redirect to the last viewed project or the first project
    if (!isLoading && !isEmpty) {
      // If there's a last viewed project and it exists in the current projects list
      if (lastViewedProjectId && projects?.some((p) => p.id === lastViewedProjectId)) {
        router.push(paths.project.details(lastViewedProjectId));
      }
      // Otherwise, redirect to the first project
      else if (projects && projects.length > 0) {
        router.push(paths.project.details(projects[0].id));
      }
    }
  }, [isLoading, isEmpty, projects, lastViewedProjectId, router]);

  // This is just loading screen for transition to the project details page
  return <LoadingScreen />;
};

export default ProjectsView;
