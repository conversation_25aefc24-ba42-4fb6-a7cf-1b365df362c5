import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { Stack, Tooltip, Typography, IconButton, CircularProgress } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface NotesPanelHeaderProps {
  totalNotes: number;
  filteredNotes: number;
  filtersExpanded: boolean;
  canEdit?: boolean;
  isCreating?: boolean;
  onFiltersToggle: () => void;
  onAddNote?: () => void;
}

const NotesPanelHeader = ({
  totalNotes,
  filteredNotes,
  filtersExpanded,
  canEdit = true,
  isCreating = false,
  onFiltersToggle,
  onAddNote,
}: NotesPanelHeaderProps) => {
  const displayText =
    totalNotes === filteredNotes
      ? `Notes: ${totalNotes}`
      : `Notes: ${filteredNotes} of ${totalNotes}`;

  return (
    <Stack direction="row" alignItems="center" justifyContent="space-between">
      <Typography variant="subtitle2">{displayText}</Typography>

      <Stack direction="row" alignItems="center">
        <Tooltip title={filtersExpanded ? 'Hide filters' : 'Show filters'} placement="top">
          <IconButton size="small" onClick={onFiltersToggle}>
            <Iconify
              icon="material-symbols:filter-alt-outline"
              sx={{ color: filtersExpanded ? 'primary.main' : 'text.secondary' }}
            />
          </IconButton>
        </Tooltip>

        {canEdit && onAddNote && (
          <IconButton disabled={isCreating} onClick={onAddNote} sx={{ p: 0 }}>
            {isCreating ? <CircularProgress size={20} /> : <AddRoundedIcon />}
          </IconButton>
        )}
      </Stack>
    </Stack>
  );
};

export default NotesPanelHeader;
