import type { SpeedDialProps } from '@mui/material';

import { useDispatch } from 'react-redux';
import { useBoolean } from 'minimal-shared/hooks';

import MicNoneIcon from '@mui/icons-material/MicNone';
import SpeedDialIcon from '@mui/material/SpeedDialIcon';
import { styled, SpeedDial, SpeedDialAction } from '@mui/material';
import VideoCameraFrontOutlinedIcon from '@mui/icons-material/VideoCameraFrontOutlined';
import RadioButtonCheckedRoundedIcon from '@mui/icons-material/RadioButtonCheckedRounded';

import useFeatureFlags from 'src/hooks/feature-flags';

import { AppFeatures } from 'src/types';
import { CONFIG } from 'src/global-config';
import { setIsLiveTranscription } from 'src/store/slices/live-transcription/slice';

import { Iconify } from 'src/components/iconify';

import UploadFileDialog from '../dialogs/upload-dialog';
import StartRecordingDialog from '../dialogs/start-recording-dialog';

interface Props extends Partial<SpeedDialProps> {
  projectId: string;
  canEdit?: boolean;
  appendActions?: React.ReactNode;
}

const StyledSpeedDial = styled(SpeedDial)(({ theme }) => ({
  '.MuiSpeedDial-fab': {
    width: 36,
    height: 36,
  },
  '&.MuiSpeedDial-directionUp, &.MuiSpeedDial-directionLeft': {
    bottom: theme.spacing(1),
    right: theme.spacing(1),
  },
  '&.MuiSpeedDial-directionDown, &.MuiSpeedDial-directionRight': {
    top: theme.spacing(1),
    left: theme.spacing(1),
  },
}));

const ProjectActions: React.FC<Props> = ({
  projectId,
  canEdit = true,
  sx = {},
  appendActions,
  ...props
}) => {
  const uploadDialog = useBoolean();
  const recordingDialog = useBoolean();
  const dispatch = useDispatch();
  const { isFlagEnabled } = useFeatureFlags();
  const isLiveTranscribeEnabled = isFlagEnabled(AppFeatures.LIVE_TRANSCRIBE);

  const goToCloudlab = () => {
    window.open(CONFIG.cloudLabUrl, '_blank');
  };

  const handleTranscribeClick = () => {
    dispatch(setIsLiveTranscription(true));
  };

  const actions = [
    {
      name: 'Upload file',
      icon: <Iconify icon="material-symbols:upload-rounded" />,
      onClick: uploadDialog.onTrue,
    },
    {
      name: 'Start recording',
      icon: <RadioButtonCheckedRoundedIcon fontSize="medium" />,
      onClick: recordingDialog.onTrue,
    },
    {
      name: 'Go to Cloudlab',
      icon: <VideoCameraFrontOutlinedIcon fontSize="medium" />,
      onClick: goToCloudlab,
    },
    ...(isLiveTranscribeEnabled
      ? [
          {
            name: 'Live Transcribe',
            icon: <MicNoneIcon fontSize="medium" />,
            onClick: handleTranscribeClick,
          },
        ]
      : []),
  ];

  // Don't render if user doesn't have edit permission
  if (!canEdit) {
    return null;
  }

  return (
    <>
      <StyledSpeedDial
        ariaLabel="Project actions"
        icon={<SpeedDialIcon />}
        direction="right"
        {...props}
      >
        {actions.map((action) => (
          <SpeedDialAction
            FabProps={{
              size: 'small',
            }}
            onClick={action.onClick}
            key={action.name}
            icon={action.icon}
            tooltipTitle={action.name}
          />
        ))}
      </StyledSpeedDial>

      {/* Dialogs */}
      {uploadDialog.value && (
        <UploadFileDialog
          open={uploadDialog.value}
          onClose={uploadDialog.onFalse}
          projectId={projectId}
        />
      )}
      {recordingDialog.value && (
        <StartRecordingDialog
          open={recordingDialog.value}
          onClose={recordingDialog.onFalse}
          projectId={projectId}
        />
      )}
    </>
  );
};

export default ProjectActions;
