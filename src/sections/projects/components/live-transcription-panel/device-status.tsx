import type { SxProps } from '@mui/material';

import { Box, Typography } from '@mui/material';

interface Props {
  isRecording: boolean;
  isConnected: boolean;
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: string | null;
  sx?: SxProps;
}

const DeviceStatus: React.FC<Props> = ({
  isRecording,
  isConnected,
  connectionState,
  error,
  sx = {},
}) => {
  const getDeviceStatus = () => {
    if (isRecording) return { color: 'success', text: 'Recording' };
    if (isConnected) return { color: 'info', text: 'Connected' };
    if (connectionState === 'connecting') return { color: 'warning', text: 'Connecting...' };
    if (error) return { color: 'error', text: 'Error' };
    return { color: 'default', text: 'Ready' };
  };

  const deviceStatus = getDeviceStatus();

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        p: 0.5,
        ...sx,
      }}
    >
      {isRecording && (
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: 'success.main',
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.5 },
              '100%': { opacity: 1 },
            },
          }}
        />
      )}
      <Typography
        variant="caption"
        color={`${deviceStatus.color}.main`}
        sx={{ fontSize: 11, fontWeight: 600 }}
      >
        {deviceStatus.text}
      </Typography>
    </Box>
  );
};

export default DeviceStatus;
