import type { SxProps } from '@mui/material';

import { useMemo } from 'react';

import { Box, Stack, Select, MenuItem, InputLabel, Typography, FormControl } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface Props {
  audioDevices: MediaDeviceInfo[];
  selectedAudioDeviceId: string;
  onDeviceChange: (deviceId: string) => void;
  sx?: SxProps;
}

const AudioDeviceSelector: React.FC<Props> = ({
  audioDevices,
  selectedAudioDeviceId,
  onDeviceChange,
  sx = {},
}) => {
  // Memoize filtered audio devices for better performance
  const audioInputDevices = useMemo(
    () => audioDevices.filter((device) => device.kind === 'audioinput'),
    [audioDevices]
  );

  return (
    <Stack spacing={1.5} sx={{ ...sx }}>
      <FormControl fullWidth size="small">
        <InputLabel id="select-microphone-label" sx={{ fontSize: 12 }}>
          Microphone
        </InputLabel>
        <Select
          labelId="select-microphone-label"
          label="Microphone"
          value={selectedAudioDeviceId || ''}
          onChange={(e) => onDeviceChange(e.target.value)}
          disabled={false} // Allow switching during recording
          sx={{ fontSize: 12, '& .MuiSelect-select': { py: 1 } }}
        >
          {audioInputDevices.map((device) => (
            <MenuItem key={device.deviceId} value={device.deviceId}>
              <Stack direction="row" alignItems="center" spacing={1} sx={{ width: '100%' }}>
                <Iconify icon="material-symbols:mic-outline" width={14} />
                <Typography variant="body2" noWrap sx={{ flex: 1, fontSize: 12 }}>
                  {device.label || `Microphone ${device.deviceId.slice(0, 8)}...`}
                </Typography>
                {device.deviceId === selectedAudioDeviceId && (
                  <Iconify icon="material-symbols:check" width={14} color="success.main" />
                )}
              </Stack>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {audioInputDevices.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 1.5 }}>
          <Iconify
            icon="material-symbols:mic-off"
            width={24}
            color="text.disabled"
            sx={{ mb: 0.5 }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: 12 }}>
            No audio devices found
          </Typography>
          <Typography variant="caption" color="text.disabled" sx={{ fontSize: 10 }}>
            Please connect a microphone and grant permission
          </Typography>
        </Box>
      )}
    </Stack>
  );
};

export default AudioDeviceSelector;
