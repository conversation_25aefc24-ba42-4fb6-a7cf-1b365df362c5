import type { SxProps } from '@mui/material';

import { useState } from 'react';

import { Stack, Button, Tooltip } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface Props {
  isRecording: boolean;
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: string | null;
  onStartRecording: () => Promise<void>;
  onStopRecording: () => Promise<void>;
  onEndSession: () => void;
  sx?: SxProps;
}

const RecordingControls: React.FC<Props> = ({
  isRecording,
  connectionState,
  error,
  onStartRecording,
  onStopRecording,
  onEndSession,
  sx = {},
}) => {
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);

  const handleStartRecording = async () => {
    setIsStarting(true);
    try {
      await onStartRecording();
    } finally {
      setIsStarting(false);
    }
  };

  const handleStopRecording = async () => {
    setIsStopping(true);
    try {
      await onStopRecording();
    } finally {
      setIsStopping(false);
    }
  };

  const canStartRecording = !isRecording && !error && connectionState !== 'connecting';
  const canStopRecording = isRecording && !isStopping;

  return (
    <Stack direction="row" spacing={1} sx={{ alignItems: 'center', ...sx }}>
      {/* Start/Stop Recording Button */}
      {!isRecording ? (
        <Tooltip
          arrow
          title={
            error
              ? 'Fix the error before starting'
              : !canStartRecording
                ? 'Please wait for connection'
                : 'Start recording with microphone + screen audio capture (for meeting participants)'
          }
        >
          <Button
            variant="contained"
            color="success"
            size="small"
            startIcon={
              isStarting ? (
                <Iconify icon="eos-icons:loading" width={16} />
              ) : (
                <Iconify icon="material-symbols:play-arrow" width={16} />
              )
            }
            onClick={handleStartRecording}
            disabled={!canStartRecording || isStarting}
            fullWidth
          >
            {isStarting ? 'Starting...' : 'Start Recording'}
          </Button>
        </Tooltip>
      ) : (
        <Button
          variant="contained"
          color="warning"
          size="small"
          startIcon={
            isStopping ? (
              <Iconify icon="eos-icons:loading" width={16} />
            ) : (
              <Iconify icon="material-symbols:pause" width={16} />
            )
          }
          onClick={handleStopRecording}
          disabled={!canStopRecording}
          fullWidth
        >
          {isStopping ? 'Stopping...' : 'Pause'}
        </Button>
      )}

      {/* End Session Button */}
      <Button
        variant="outlined"
        color="error"
        size="small"
        startIcon={<Iconify icon="material-symbols:stop" width={16} />}
        onClick={onEndSession}
        fullWidth
      >
        End
      </Button>
    </Stack>
  );
};

export default RecordingControls;
