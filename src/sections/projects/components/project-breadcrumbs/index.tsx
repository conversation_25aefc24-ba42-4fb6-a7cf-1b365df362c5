import { useDispatch } from 'react-redux';
import { isActiveLink } from 'minimal-shared/utils';

import { Stack } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useParams, usePathname } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { openNavigationDrawer } from 'src/store/slices/ui';

import { ProjectPopover } from './project-popover';

const ProjectBreadcrumbs: React.FC = () => {
  const pathname = usePathname();
  const { id: projectId = '' } = useParams();
  const { projects } = useUserInitialContext();

  const dispatch = useDispatch();

  const showBreadcrumbs = isActiveLink(pathname, paths.project.details(projectId));
  const currentProject = projects.find((project) => project.id === projectId);

  const handleOpenDrawer = () => {
    console.log('openDrawer');
    dispatch(openNavigationDrawer());
  };

  if (!showBreadcrumbs || !currentProject) return null;

  return (
    <Stack
      direction="row"
      alignItems="center"
      onClick={handleOpenDrawer}
      sx={{
        cursor: 'pointer',
        '&:hover': {
          opacity: 0.8,
        },
      }}
    >
      <ProjectPopover showAsLink data={projects} currentProject={currentProject} />
    </Stack>
  );
};

export default ProjectBreadcrumbs;
