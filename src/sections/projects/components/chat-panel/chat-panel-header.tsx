import type { GeminiModelType } from 'src/lib/firebase';

import { Stack, Switch, Tooltip, Typography, FormControlLabel } from '@mui/material';

interface ChatPanelHeaderProps {
  selectedModel: GeminiModelType;
  onModelChange: (model: GeminiModelType) => void;
}

const ChatPanelHeader = ({ selectedModel, onModelChange }: ChatPanelHeaderProps) => {
  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newModel: GeminiModelType = event.target.checked ? 'gemini-2.5-pro' : 'gemini-2.5-flash';
    onModelChange(newModel);
  };

  return (
    <Stack direction="row" alignItems="center" gap={2} sx={{ mb: 2 }}>
      <Typography variant="subtitle2">Chat</Typography>

      <Tooltip arrow title="Advanced" placement="top">
        <FormControlLabel
          label=""
          control={
            <Switch
              checked={selectedModel === 'gemini-2.5-pro'}
              onChange={handleSwitchChange}
              size="medium"
            />
          }
        />
      </Tooltip>
    </Stack>
  );
};

export default ChatPanelHeader;
