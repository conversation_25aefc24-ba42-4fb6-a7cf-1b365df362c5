import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';

import { useRef, useEffect } from 'react';

import { Paper } from '@mui/material';

import useAiAgent from 'src/hooks/ai-agent';

import ChatBox from 'src/sections/chat/chat-box';

import ProjectActions from '../project-actions';

interface ChatPanelProps {
  selectedFiles: Resource[];
  projectId: string;
  canEdit?: boolean;
  sx?: SxProps;
}

const QUICK_REPLY_OPTIONS = ['Summarise', 'Discuss', 'Evaluate'];

const ChatPanel: React.FC<ChatPanelProps> = ({
  selectedFiles,
  projectId,
  canEdit = true,
  sx = {},
}) => {
  const {
    messages,
    isReplying,
    allowSendMessage,
    sendMessage,
    isStartingConversation,
    startConversationWithResources,
    updateContextWithNewResources,
    removeResourcesFromContext,
    hasActiveSession,
    resetAgent,
    stopConversation,
  } = useAiAgent();

  const prevSelectedFilesRef = useRef<Resource[]>([]);
  const prevProjectIdRef = useRef<string>(projectId);

  // Reset AI agent and restart conversation when projectId changes
  useEffect(() => {
    if (prevProjectIdRef.current !== projectId) {
      resetAgent();
      prevSelectedFilesRef.current = [];
      prevProjectIdRef.current = projectId;
      startConversationWithResources([]);
    }
  }, [projectId, resetAgent, startConversationWithResources]);

  // Start conversation on initial mount
  useEffect(() => {
    if (!hasActiveSession && prevProjectIdRef.current === projectId) {
      startConversationWithResources([]);
    }
  }, [hasActiveSession, startConversationWithResources, projectId]);

  // Handle selected files updates
  useEffect(() => {
    if (!hasActiveSession) {
      return;
    }

    const prevFiles = prevSelectedFilesRef.current;
    const newFiles = selectedFiles.filter(
      (file) => !prevFiles.some((prevFile) => prevFile.id === file.id)
    );
    const removedFiles = prevFiles.filter(
      (prevFile) => !selectedFiles.some((file) => file.id === prevFile.id)
    );

    if (newFiles.length > 0) {
      updateContextWithNewResources(newFiles);
    }

    if (removedFiles.length > 0) {
      removeResourcesFromContext(removedFiles);
    }

    prevSelectedFilesRef.current = selectedFiles;
  }, [
    selectedFiles,
    updateContextWithNewResources,
    removeResourcesFromContext,
    hasActiveSession,
    projectId,
  ]);

  return (
    <Paper
      elevation={1}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '50%',
        },
        overflow: 'hidden',
        ...sx,
      }}
    >
      <ChatBox
        containerSx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          minHeight: 0,
        }}
        messageListSx={{
          flex: 1,
          minHeight: 0,
          pt: 2,
          px: 0,
          pr: 1,
        }}
        inputSx={{
          flexShrink: 0,
        }}
        messages={messages}
        loading={isStartingConversation}
        isReplying={isReplying}
        disabled={!allowSendMessage}
        sendMessage={sendMessage}
        stopConversation={stopConversation}
        quickReplyOptions={QUICK_REPLY_OPTIONS}
        actions={<ProjectActions projectId={projectId} canEdit={canEdit} />}
      />
    </Paper>
  );
};

export default ChatPanel;
