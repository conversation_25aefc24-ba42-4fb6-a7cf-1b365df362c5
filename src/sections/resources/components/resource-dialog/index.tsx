import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useTheme, useMediaQuery } from '@mui/material';

import { viewResource } from 'src/store/slices/resources/slice';
import { useLazyGetResourceQuery } from 'src/store/api/resources';
import { selectFocusedResource } from 'src/store/slices/resources/selectors';

import ResourceDialogMobile from './mobile';
import ResourceDialogDesktop from './desktop';

export enum MainPanel {
  Transcript = 'transcript',
  Notes = 'notes',
  Preview = 'preview',
}

const ResourceDialog: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const selectedResource = useSelector(selectFocusedResource);
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('lg'));

  const [getResource, { data: resource, isLoading, isFetching }] = useLazyGetResourceQuery({
    refetchOnFocus: false,
  });

  useEffect(() => {
    if (!selectedResource?.id) return;
    getResource({ id: selectedResource.id });
  }, [selectedResource?.id]);

  const onClose = () => {
    dispatch(viewResource(null));
  };

  const loading = isFetching || isLoading;

  if (!selectedResource) return null;

  if (isSmallScreen) {
    return <ResourceDialogMobile loading={loading} resource={resource} onClose={onClose} />;
  }

  return <ResourceDialogDesktop loading={loading} resource={resource} onClose={onClose} />;
};

export default ResourceDialog;
