import { useState } from 'react';

import { Box, Card, Fade, Typography } from '@mui/material';

import { LoadingScreen } from 'src/components/loading-screen';

import type { ResourceItem } from '../../../resources-list';

const ResourceImagePreviewer: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  // If no URL, show error message
  if (!data.url) {
    return (
      <Box
        sx={{
          height: 400,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          borderRadius: 1,
          border: '1px dashed',
          borderColor: 'divider',
        }}
      >
        <Typography variant="body2" color="text.secondary" textAlign="center">
          Image URL not available
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          The file may still be processing
        </Typography>
      </Box>
    );
  }

  if (hasError) {
    return (
      <Box
        sx={{
          height: 400,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          borderRadius: 1,
          border: '1px dashed',
          borderColor: 'error.main',
        }}
      >
        <Typography variant="body2" color="error" textAlign="center">
          Failed to load image
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          The file may not be ready yet or the URL is invalid
        </Typography>
      </Box>
    );
  }

  return (
    <Card
      sx={{
        width: '100%',
        maxHeight: 600,
        position: 'relative',
        borderRadius: 1,
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.paper',
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}

      <Fade in={!isLoading} timeout={300}>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <img
            src={data.url}
            alt={data.name || 'Image preview'}
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              display: 'block',
            }}
          />
        </Box>
      </Fade>
    </Card>
  );
};

export default ResourceImagePreviewer;
