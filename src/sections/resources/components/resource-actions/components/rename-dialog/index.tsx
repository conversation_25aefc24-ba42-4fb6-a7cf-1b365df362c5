import { z as zod } from 'zod';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { hasFileExtension, getDisplayFileName } from 'src/utils/file-utils';

import { useUpdateResourceMutation } from 'src/store/api/resources';

import { Form, Field } from 'src/components/hook-form';

import type { ResourceItem } from '../../../resources-list';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: () => void;
  resource: ResourceItem;
};

interface FormValues {
  name: string;
}

const RenameResourceDialog: React.FC<Props> = ({ open, onClose, resource }) => {
  const [updateResource, { isLoading: isUpdatingResource }] = useUpdateResourceMutation();

  const isMarkdownFile = hasFileExtension(resource.name, 'md');
  const displayName = getDisplayFileName(resource.name);

  const methods = useForm<FormValues>({
    defaultValues: {
      name: displayName,
    },
    resolver: zodResolver(
      zod.object({
        name: zod.string().min(1, { message: 'Please input resource name' }),
      })
    ),
  });

  const {
    handleSubmit,
    formState: { errors, isValid },
  } = methods;

  const onSubmit = async (data: FormValues) => {
    try {
      // For .md files, add the extension back when saving
      const finalName =
        isMarkdownFile && !data.name.endsWith('.md') ? `${data.name}.md` : data.name;

      await updateResource({
        id: resource.id,
        payload: {
          name: finalName,
        },
      }).unwrap();

      onClose();
      toast.success('File name updated successfully');
    } catch (error) {
      toast.error('Failed to update file name');
    }
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>Rename</DialogTitle>
      <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
          <Field.Text name="name" label="File name" fullWidth error={!!errors.name} />
        </DialogContent>
        <DialogActions>
          <LoadingButton
            disabled={!isValid}
            variant="contained"
            type="submit"
            loading={isUpdatingResource}
          >
            Update
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
};

export default RenameResourceDialog;
